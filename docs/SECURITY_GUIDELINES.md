# Security Guidelines - NGA UI

## Table of Contents
- [Authentication & Authorization](#authentication--authorization)
- [Data Protection](#data-protection)
- [Input Validation](#input-validation)
- [XSS Prevention](#xss-prevention)
- [CSRF Protection](#csrf-protection)
- [Secure Communication](#secure-communication)
- [Dependency Security](#dependency-security)
- [Environment Security](#environment-security)

## Authentication & Authorization

### 1. Token Management
```javascript
// ✅ Good - Secure token storage
class TokenService {
  static setToken(token) {
    // Use httpOnly cookies for sensitive tokens when possible
    // For SPA, use secure storage with proper expiration
    const expirationTime = Date.now() + (24 * 60 * 60 * 1000); // 24 hours
    sessionStorage.setItem('authToken', token);
    sessionStorage.setItem('tokenExpiration', expirationTime.toString());
  }

  static getToken() {
    const token = sessionStorage.getItem('authToken');
    const expiration = sessionStorage.getItem('tokenExpiration');
    
    if (!token || !expiration) return null;
    
    if (Date.now() > parseInt(expiration)) {
      this.clearToken();
      return null;
    }
    
    return token;
  }

  static clearToken() {
    sessionStorage.removeItem('authToken');
    sessionStorage.removeItem('tokenExpiration');
    localStorage.removeItem('refreshToken');
  }
}

// ❌ Avoid - Insecure token storage
localStorage.setItem('password', userPassword); // Never store passwords
window.authToken = token; // Never expose tokens globally
```

### 2. Route Protection
```javascript
// Protected route component
const ProtectedRoute = ({ children, requiredPermissions = [] }) => {
  const { isAuthenticated, user, permissions } = useAuth();
  
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }
  
  // Check permissions
  const hasRequiredPermissions = requiredPermissions.every(
    permission => permissions.includes(permission)
  );
  
  if (requiredPermissions.length > 0 && !hasRequiredPermissions) {
    return <Navigate to="/unauthorized" replace />;
  }
  
  return children;
};

// Usage
<ProtectedRoute requiredPermissions={['user:read', 'user:write']}>
  <UserManagementPage />
</ProtectedRoute>
```

### 3. Session Management
```javascript
// Session timeout handling
const useSessionTimeout = () => {
  const dispatch = useDispatch();
  const timeoutRef = useRef(null);
  
  const resetTimeout = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    // Set timeout for 30 minutes of inactivity
    timeoutRef.current = setTimeout(() => {
      dispatch(logout());
      window.location.href = '/login?reason=timeout';
    }, 30 * 60 * 1000);
  }, [dispatch]);
  
  useEffect(() => {
    // Reset timeout on user activity
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    
    events.forEach(event => {
      document.addEventListener(event, resetTimeout, true);
    });
    
    resetTimeout(); // Initial timeout
    
    return () => {
      events.forEach(event => {
        document.removeEventListener(event, resetTimeout, true);
      });
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [resetTimeout]);
};
```

## Data Protection

### 1. Sensitive Data Handling
```javascript
// ✅ Good - Mask sensitive data
const maskCreditCard = (cardNumber) => {
  if (!cardNumber || cardNumber.length < 4) return '';
  return '**** **** **** ' + cardNumber.slice(-4);
};

const maskEmail = (email) => {
  if (!email) return '';
  const [username, domain] = email.split('@');
  if (username.length <= 2) return email;
  return username[0] + '*'.repeat(username.length - 2) + username.slice(-1) + '@' + domain;
};

// ✅ Good - Secure data transmission
const sendSensitiveData = async (data) => {
  // Encrypt sensitive fields before sending
  const encryptedData = {
    ...data,
    ssn: encrypt(data.ssn),
    creditCard: encrypt(data.creditCard),
  };
  
  return httpService.post('/api/sensitive-data', encryptedData);
};

// ❌ Avoid - Logging sensitive data
console.log('User data:', userData); // May contain sensitive info
console.log('API response:', response); // May contain tokens or sensitive data
```

### 2. Data Sanitization
```javascript
// Sanitize user input before display
import DOMPurify from 'dompurify';

const sanitizeHtml = (html) => {
  return DOMPurify.sanitize(html, {
    ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'p', 'br'],
    ALLOWED_ATTR: []
  });
};

// Usage in component
const UserComment = ({ comment }) => {
  const sanitizedComment = sanitizeHtml(comment);
  
  return (
    <div 
      dangerouslySetInnerHTML={{ __html: sanitizedComment }}
    />
  );
};
```

## Input Validation

### 1. Client-Side Validation
```javascript
// Validation schemas using Yup
import * as Yup from 'yup';

const userValidationSchema = Yup.object({
  email: Yup.string()
    .email('Invalid email format')
    .required('Email is required')
    .max(254, 'Email too long'),
  
  password: Yup.string()
    .min(8, 'Password must be at least 8 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
      'Password must contain uppercase, lowercase, number and special character'
    )
    .required('Password is required'),
  
  phone: Yup.string()
    .matches(/^\+?[\d\s\-\(\)]+$/, 'Invalid phone number format')
    .min(10, 'Phone number too short')
    .max(15, 'Phone number too long'),
});

// Input sanitization
const sanitizeInput = (input) => {
  if (typeof input !== 'string') return input;
  
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .slice(0, 1000); // Limit length
};
```

### 2. File Upload Security
```javascript
const SecureFileUpload = ({ onFileSelect }) => {
  const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf'];
  const maxSize = 5 * 1024 * 1024; // 5MB
  
  const validateFile = (file) => {
    // Check file type
    if (!allowedTypes.includes(file.type)) {
      throw new Error('File type not allowed');
    }
    
    // Check file size
    if (file.size > maxSize) {
      throw new Error('File size too large');
    }
    
    // Check file name for malicious patterns
    const dangerousPatterns = /[<>:"/\\|?*\x00-\x1f]/;
    if (dangerousPatterns.test(file.name)) {
      throw new Error('Invalid file name');
    }
    
    return true;
  };
  
  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (!file) return;
    
    try {
      validateFile(file);
      onFileSelect(file);
    } catch (error) {
      alert(error.message);
      event.target.value = ''; // Clear input
    }
  };
  
  return (
    <input
      type="file"
      accept={allowedTypes.join(',')}
      onChange={handleFileChange}
    />
  );
};
```

## XSS Prevention

### 1. Safe HTML Rendering
```javascript
// ✅ Good - Use React's built-in XSS protection
const UserProfile = ({ user }) => {
  return (
    <div>
      <h1>{user.name}</h1> {/* Automatically escaped */}
      <p>{user.bio}</p>     {/* Automatically escaped */}
    </div>
  );
};

// ✅ Good - When HTML is needed, sanitize it
const RichTextDisplay = ({ content }) => {
  const sanitizedContent = DOMPurify.sanitize(content);
  
  return (
    <div dangerouslySetInnerHTML={{ __html: sanitizedContent }} />
  );
};

// ❌ Avoid - Direct HTML injection
const UnsafeComponent = ({ userInput }) => {
  return <div dangerouslySetInnerHTML={{ __html: userInput }} />; // XSS risk
};
```

### 2. Content Security Policy
```html
<!-- Add to index.html -->
<meta http-equiv="Content-Security-Policy" content="
  default-src 'self';
  script-src 'self' 'unsafe-inline' https://trusted-cdn.com;
  style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
  img-src 'self' data: https:;
  font-src 'self' https://fonts.gstatic.com;
  connect-src 'self' https://api.example.com;
  frame-ancestors 'none';
">
```

## CSRF Protection

### 1. CSRF Token Handling
```javascript
// CSRF token management
class CSRFService {
  static async getToken() {
    const response = await fetch('/api/csrf-token', {
      credentials: 'include'
    });
    const { token } = await response.json();
    return token;
  }
  
  static async makeSecureRequest(url, options = {}) {
    const csrfToken = await this.getToken();
    
    return fetch(url, {
      ...options,
      credentials: 'include',
      headers: {
        ...options.headers,
        'X-CSRF-Token': csrfToken,
        'Content-Type': 'application/json',
      },
    });
  }
}

// HTTP service with CSRF protection
class SecureHTTPService {
  async post(url, data) {
    return CSRFService.makeSecureRequest(url, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }
}
```

## Secure Communication

### 1. HTTPS Enforcement
```javascript
// Redirect to HTTPS in production
if (process.env.NODE_ENV === 'production' && window.location.protocol !== 'https:') {
  window.location.replace(`https:${window.location.href.substring(window.location.protocol.length)}`);
}

// HTTP service with secure defaults
const httpService = axios.create({
  baseURL: process.env.REACT_APP_API_URL,
  timeout: 10000,
  withCredentials: true, // Include cookies for CSRF protection
  headers: {
    'X-Requested-With': 'XMLHttpRequest', // CSRF protection
  },
});
```

### 2. API Security Headers
```javascript
// Request interceptor to add security headers
httpService.interceptors.request.use((config) => {
  // Add security headers
  config.headers['X-Content-Type-Options'] = 'nosniff';
  config.headers['X-Frame-Options'] = 'DENY';
  config.headers['X-XSS-Protection'] = '1; mode=block';
  
  return config;
});
```

## Dependency Security

### 1. Package Vulnerability Scanning
```bash
# Regular security audits
npm audit
npm audit fix

# Use npm-check-updates to keep dependencies current
npx npm-check-updates -u
npm install

# Check for known vulnerabilities
npx audit-ci --moderate
```

### 2. Secure Package Management
```json
// package.json - Use exact versions for security-critical packages
{
  "dependencies": {
    "react": "18.2.0",
    "axios": "0.27.2"
  },
  "scripts": {
    "audit": "npm audit",
    "audit:fix": "npm audit fix",
    "security:check": "npx audit-ci --moderate"
  }
}
```

## Environment Security

### 1. Environment Variables
```javascript
// ✅ Good - Secure environment variable usage
const config = {
  apiUrl: process.env.REACT_APP_API_URL,
  environment: process.env.NODE_ENV,
  // Never expose sensitive data in client-side env vars
};

// ❌ Avoid - Exposing secrets in client
const badConfig = {
  apiKey: process.env.REACT_APP_SECRET_KEY, // Exposed to client
  dbPassword: process.env.REACT_APP_DB_PASS, // Never do this
};
```

### 2. Build Security
```javascript
// Remove console logs in production
if (process.env.NODE_ENV === 'production') {
  console.log = () => {};
  console.warn = () => {};
  console.error = () => {};
}

// Disable React DevTools in production
if (process.env.NODE_ENV === 'production') {
  if (typeof window.__REACT_DEVTOOLS_GLOBAL_HOOK__ === 'object') {
    window.__REACT_DEVTOOLS_GLOBAL_HOOK__.onCommitFiberRoot = undefined;
    window.__REACT_DEVTOOLS_GLOBAL_HOOK__.onCommitFiberUnmount = undefined;
  }
}
```

## Security Monitoring

### 1. Error Handling
```javascript
// Secure error handling - don't expose sensitive information
const handleApiError = (error) => {
  // Log detailed error for debugging (server-side only)
  if (process.env.NODE_ENV === 'development') {
    console.error('API Error:', error);
  }
  
  // Show generic error to user
  const userMessage = error.response?.status === 401 
    ? 'Please log in again'
    : 'An error occurred. Please try again.';
    
  showErrorToast(userMessage);
};
```

### 2. Security Headers Validation
```javascript
// Validate security headers in responses
const validateSecurityHeaders = (response) => {
  const requiredHeaders = [
    'x-content-type-options',
    'x-frame-options',
    'x-xss-protection'
  ];
  
  requiredHeaders.forEach(header => {
    if (!response.headers[header]) {
      console.warn(`Missing security header: ${header}`);
    }
  });
};
```

## Security Checklist

Before deploying:
- [ ] All user inputs are validated and sanitized
- [ ] Authentication and authorization implemented
- [ ] Sensitive data is properly masked/encrypted
- [ ] HTTPS is enforced in production
- [ ] Content Security Policy is configured
- [ ] Dependencies are up to date and audited
- [ ] Error messages don't expose sensitive information
- [ ] Session timeout is implemented
- [ ] File uploads are properly validated
- [ ] Security headers are configured
- [ ] No sensitive data in client-side code
- [ ] CSRF protection is implemented
