# Style Guidelines - NGA UI

## Table of Contents
- [SCSS Architecture](#scss-architecture)
- [Naming Conventions](#naming-conventions)
- [Component Styling](#component-styling)
- [Material-UI Integration](#material-ui-integration)
- [Responsive Design](#responsive-design)
- [Theme Management](#theme-management)
- [Performance Optimization](#performance-optimization)

## SCSS Architecture

### 1. File Structure
```
src/assets/styles/
├── abstracts/
│   ├── _variables.scss
│   ├── _mixins.scss
│   └── _functions.scss
├── base/
│   ├── _reset.scss
│   ├── _typography.scss
│   └── _base.scss
├── components/
│   ├── _buttons.scss
│   ├── _forms.scss
│   └── _cards.scss
├── layout/
│   ├── _header.scss
│   ├── _sidebar.scss
│   └── _footer.scss
├── pages/
│   ├── _home.scss
│   └── _dashboard.scss
├── themes/
│   ├── _light.scss
│   └── _dark.scss
└── main.scss
```

### 2. Import Order
```scss
// main.scss
// 1. Abstracts (variables, mixins, functions)
@import 'abstracts/variables';
@import 'abstracts/mixins';
@import 'abstracts/functions';

// 2. Base styles
@import 'base/reset';
@import 'base/typography';
@import 'base/base';

// 3. Layout
@import 'layout/header';
@import 'layout/sidebar';

// 4. Components
@import 'components/buttons';
@import 'components/forms';

// 5. Pages
@import 'pages/home';
@import 'pages/dashboard';

// 6. Themes
@import 'themes/light';
@import 'themes/dark';
```

### 3. Variables Organization
```scss
// _variables.scss
// Colors
:root {
  // Primary colors
  --primary-50: #e3f2fd;
  --primary-100: #bbdefb;
  --primary-500: #2196f3;
  --primary-900: #0d47a1;

  // Secondary colors
  --secondary-50: #fce4ec;
  --secondary-500: #e91e63;
  --secondary-900: #880e4f;

  // Neutral colors
  --gray-50: #fafafa;
  --gray-100: #f5f5f5;
  --gray-500: #9e9e9e;
  --gray-900: #212121;

  // Semantic colors
  --success: #4caf50;
  --warning: #ff9800;
  --error: #f44336;
  --info: #2196f3;
}

// Spacing
$spacing-xs: 0.25rem;   // 4px
$spacing-sm: 0.5rem;    // 8px
$spacing-md: 1rem;      // 16px
$spacing-lg: 1.5rem;    // 24px
$spacing-xl: 2rem;      // 32px
$spacing-xxl: 3rem;     // 48px

// Typography
$font-family-primary: 'Roboto', sans-serif;
$font-family-secondary: 'Open Sans', sans-serif;

$font-size-xs: 0.75rem;   // 12px
$font-size-sm: 0.875rem;  // 14px
$font-size-md: 1rem;      // 16px
$font-size-lg: 1.125rem;  // 18px
$font-size-xl: 1.25rem;   // 20px

// Breakpoints
$breakpoint-xs: 0;
$breakpoint-sm: 600px;
$breakpoint-md: 960px;
$breakpoint-lg: 1280px;
$breakpoint-xl: 1920px;

// Z-index scale
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;
```

## Naming Conventions

### 1. BEM Methodology
Use Block Element Modifier (BEM) naming convention:

```scss
// Block
.user-card {
  padding: $spacing-md;
  border-radius: 8px;
  background: var(--gray-50);

  // Element
  &__header {
    display: flex;
    justify-content: space-between;
    margin-bottom: $spacing-sm;
  }

  &__title {
    font-size: $font-size-lg;
    font-weight: 600;
    color: var(--gray-900);
  }

  &__avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
  }

  &__content {
    line-height: 1.6;
    color: var(--gray-700);
  }

  &__actions {
    display: flex;
    gap: $spacing-sm;
    margin-top: $spacing-md;
  }

  // Modifier
  &--highlighted {
    border: 2px solid var(--primary-500);
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.15);
  }

  &--compact {
    padding: $spacing-sm;

    .user-card__title {
      font-size: $font-size-md;
    }
  }
}
```

### 2. Class Naming Rules
- Use lowercase with hyphens
- Be descriptive and semantic
- Avoid abbreviations unless commonly understood
- Use consistent prefixes for component types

```scss
// ✅ Good
.navigation-menu { }
.user-profile-card { }
.budget-calculation-form { }
.primary-button { }

// ❌ Avoid
.nav { }
.usrPrfCrd { }
.btn1 { }
.redBox { }
```

## Component Styling

### 1. Component-Specific Styles
Each component should have its own SCSS file:

```scss
// UserCard.scss
.user-card {
  // Component root styles
  display: flex;
  flex-direction: column;
  padding: $spacing-md;
  border-radius: 8px;
  background: var(--background-paper);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  // Nested elements
  &__header {
    display: flex;
    align-items: center;
    margin-bottom: $spacing-sm;
  }

  &__avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    margin-right: $spacing-sm;
  }

  &__info {
    flex: 1;
  }

  &__name {
    font-size: $font-size-md;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 4px 0;
  }

  &__email {
    font-size: $font-size-sm;
    color: var(--text-secondary);
    margin: 0;
  }

  // State modifiers
  &--loading {
    opacity: 0.6;
    pointer-events: none;
  }

  &--disabled {
    opacity: 0.4;
    cursor: not-allowed;
  }
}
```

### 2. Mixins for Reusability
```scss
// _mixins.scss
@mixin button-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-sm $spacing-md;
  border: none;
  border-radius: 4px;
  font-family: $font-family-primary;
  font-size: $font-size-sm;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

@mixin card-shadow($level: 1) {
  @if $level == 1 {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  } @else if $level == 2 {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  } @else if $level == 3 {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  }
}

@mixin responsive-text($min-size, $max-size) {
  font-size: clamp(#{$min-size}, 2.5vw, #{$max-size});
}

// Usage
.primary-button {
  @include button-base;
  background: var(--primary-500);
  color: white;

  &:hover {
    background: var(--primary-600);
  }
}

.card {
  @include card-shadow(2);
  padding: $spacing-lg;
  border-radius: 8px;
}
```

## Material-UI Integration

### 1. Theme Customization
```scss
// Override Material-UI styles when necessary
.MuiButton-root {
  text-transform: none; // Remove uppercase transformation
  border-radius: 8px;
  font-weight: 500;

  &.MuiButton-containedPrimary {
    background: var(--primary-500);
    
    &:hover {
      background: var(--primary-600);
    }
  }
}

.MuiTextField-root {
  .MuiOutlinedInput-root {
    border-radius: 8px;
    
    &.Mui-focused .MuiOutlinedInput-notchedOutline {
      border-color: var(--primary-500);
    }
  }
}
```

### 2. Custom Material-UI Components
```scss
// Custom styled Material-UI components
.custom-data-grid {
  .MuiDataGrid-root {
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .MuiDataGrid-columnHeaders {
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
  }

  .MuiDataGrid-cell {
    border-bottom: 1px solid var(--gray-100);
  }
}
```

## Responsive Design

### 1. Mobile-First Approach
```scss
// Base styles (mobile)
.navigation {
  display: flex;
  flex-direction: column;
  padding: $spacing-sm;

  // Tablet and up
  @media (min-width: $breakpoint-md) {
    flex-direction: row;
    padding: $spacing-md;
  }

  // Desktop and up
  @media (min-width: $breakpoint-lg) {
    padding: $spacing-lg;
  }
}
```

### 2. Responsive Mixins
```scss
@mixin respond-to($breakpoint) {
  @if $breakpoint == 'sm' {
    @media (min-width: $breakpoint-sm) { @content; }
  }
  @if $breakpoint == 'md' {
    @media (min-width: $breakpoint-md) { @content; }
  }
  @if $breakpoint == 'lg' {
    @media (min-width: $breakpoint-lg) { @content; }
  }
  @if $breakpoint == 'xl' {
    @media (min-width: $breakpoint-xl) { @content; }
  }
}

// Usage
.sidebar {
  width: 100%;
  
  @include respond-to('md') {
    width: 250px;
  }
  
  @include respond-to('lg') {
    width: 300px;
  }
}
```

### 3. Container Queries (Future)
```scss
// Prepare for container queries
.card-container {
  container-type: inline-size;
  
  .card {
    padding: $spacing-sm;
    
    @container (min-width: 300px) {
      padding: $spacing-md;
    }
    
    @container (min-width: 500px) {
      padding: $spacing-lg;
    }
  }
}
```

## Theme Management

### 1. CSS Custom Properties
```scss
// Light theme
:root {
  --background-default: #ffffff;
  --background-paper: #ffffff;
  --text-primary: #212121;
  --text-secondary: #757575;
  --divider: #e0e0e0;
}

// Dark theme
[data-theme="dark"] {
  --background-default: #121212;
  --background-paper: #1e1e1e;
  --text-primary: #ffffff;
  --text-secondary: #b3b3b3;
  --divider: #333333;
}
```

### 2. Theme-Aware Components
```scss
.header {
  background: var(--background-paper);
  color: var(--text-primary);
  border-bottom: 1px solid var(--divider);
  
  &__title {
    color: var(--text-primary);
  }
  
  &__subtitle {
    color: var(--text-secondary);
  }
}
```

## Performance Optimization

### 1. CSS Optimization
```scss
// Use efficient selectors
.user-list {
  // ✅ Good - specific class selector
  .user-item {
    padding: $spacing-sm;
  }
  
  // ❌ Avoid - complex nested selectors
  // .container .content .list .item .title { }
}

// Minimize repaints and reflows
.animated-element {
  // Use transform instead of changing position
  transform: translateX(0);
  transition: transform 0.3s ease;
  
  &:hover {
    transform: translateX(10px);
  }
}
```

### 2. Critical CSS
```scss
// Above-the-fold styles
.header,
.navigation,
.hero-section {
  // Critical styles here
}

// Non-critical styles can be loaded later
.footer,
.modal,
.tooltip {
  // Non-critical styles
}
```

## Stylelint Configuration

The project uses Stylelint with SCSS support:

```json
{
  "extends": ["stylelint-config-standard-scss"],
  "rules": {
    "selector-class-pattern": "^[a-z][a-z0-9]*(-[a-z0-9]+)*(__[a-z0-9]+(-[a-z0-9]+)*)?(--[a-z0-9]+(-[a-z0-9]+)*)?$",
    "scss/at-rule-no-unknown": true,
    "block-opening-brace-space-before": "always",
    "selector-list-comma-newline-after": "always"
  }
}
```

## Style Checklist

Before submitting styles:
- [ ] Follows BEM naming convention
- [ ] Uses CSS custom properties for theming
- [ ] Responsive design implemented
- [ ] No hardcoded colors (uses variables)
- [ ] Passes Stylelint validation
- [ ] Performance considerations addressed
- [ ] Accessibility guidelines followed
- [ ] Cross-browser compatibility tested
