# Testing Guidelines - NGA UI

## Table of Contents
- [Testing Philosophy](#testing-philosophy)
- [Testing Setup](#testing-setup)
- [Unit Testing](#unit-testing)
- [Component Testing](#component-testing)
- [Integration Testing](#integration-testing)
- [Testing Utilities](#testing-utilities)
- [Best Practices](#best-practices)

## Testing Philosophy

### 1. Testing Pyramid
Follow the testing pyramid approach:
- **Unit Tests (70%)**: Test individual functions and components in isolation
- **Integration Tests (20%)**: Test component interactions and API integrations
- **E2E Tests (10%)**: Test complete user workflows

### 2. Testing Principles
- Write tests that test behavior, not implementation
- Tests should be readable and maintainable
- Aim for high test coverage but focus on critical paths
- Tests should be fast and reliable
- Use descriptive test names that explain the expected behavior

## Testing Setup

### 1. Jest Configuration
The project uses Jest with custom configuration:

```javascript
// jest.config.js
const config = {
  setupFiles: ['<rootDir>/__mocks__/envTest.js'],
  setupFilesAfterEnv: ['<rootDir>/__mocks__/jest-setup.js'],
  testEnvironment: 'jsdom',
  moduleNameMapper: {
    '\\.(scss|sass|css)$': 'identity-obj-proxy',
    '^core/(.*)$': '<rootDir>/src/core/$1',
    '^features/(.*)$': '<rootDir>/src/features/$1',
    '^shared/(.*)$': '<rootDir>/src/shared/$1',
  },
  transform: {
    '^.+\\.jsx?$': 'babel-jest',
    '.+\\.(css|scss)$': 'jest-css-modules-transform',
  },
};
```

### 2. Testing Scripts
```bash
# Run tests locally
npm run test:local

# Run tests with coverage
npm run test

# Run tests in watch mode
npm test -- --watch

# Run specific test file
npm test -- UserComponent.spec.js
```

### 3. Test File Structure
```
src/
├── components/
│   ├── UserCard/
│   │   ├── UserCard.jsx
│   │   ├── UserCard.spec.js
│   │   └── __mocks__/
│   │       └── mockUserData.js
└── __tests__/
    ├── integration/
    └── utils/
```

## Unit Testing

### 1. Testing Utilities and Functions
```javascript
// core/utilities/dateUtils.spec.js
import { formatDate, isValidDate, addDays } from './dateUtils';

describe('dateUtils', () => {
  describe('formatDate', () => {
    it('should format date correctly', () => {
      const date = new Date('2023-12-25');
      expect(formatDate(date, 'YYYY-MM-DD')).toBe('2023-12-25');
    });

    it('should handle invalid date', () => {
      expect(formatDate(null)).toBe('');
    });
  });

  describe('isValidDate', () => {
    it('should return true for valid date', () => {
      expect(isValidDate(new Date())).toBe(true);
    });

    it('should return false for invalid date', () => {
      expect(isValidDate('invalid')).toBe(false);
    });
  });
});
```

### 2. Testing Services
```javascript
// features/User/services/userService.spec.js
import { userService } from './userService';
import { httpService } from 'core/services/HTTPService';

// Mock the HTTP service
jest.mock('core/services/HTTPService');

describe('UserService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getUsers', () => {
    it('should fetch users successfully', async () => {
      const mockUsers = [{ id: 1, name: 'John' }];
      httpService.get.mockResolvedValue({ data: mockUsers });

      const result = await userService.getUsers();

      expect(httpService.get).toHaveBeenCalledWith('/users', { params: {} });
      expect(result.data).toEqual(mockUsers);
    });

    it('should handle API errors', async () => {
      const mockError = new Error('API Error');
      httpService.get.mockRejectedValue(mockError);

      await expect(userService.getUsers()).rejects.toThrow('API Error');
    });
  });
});
```

### 3. Testing Redux Slices
```javascript
// features/User/state/userSlice.spec.js
import userReducer, { 
  fetchUsers, 
  clearUsers 
} from './userSlice';

describe('userSlice', () => {
  const initialState = {
    list: [],
    loading: false,
    error: null,
  };

  it('should return initial state', () => {
    expect(userReducer(undefined, {})).toEqual(initialState);
  });

  it('should handle clearUsers', () => {
    const previousState = {
      list: [{ id: 1, name: 'John' }],
      loading: false,
      error: null,
    };

    expect(userReducer(previousState, clearUsers())).toEqual(initialState);
  });

  describe('fetchUsers async thunk', () => {
    it('should handle pending state', () => {
      const action = { type: fetchUsers.pending.type };
      const state = userReducer(initialState, action);

      expect(state.loading).toBe(true);
    });

    it('should handle fulfilled state', () => {
      const users = [{ id: 1, name: 'John' }];
      const action = { 
        type: fetchUsers.fulfilled.type, 
        payload: users 
      };
      const state = userReducer(initialState, action);

      expect(state.loading).toBe(false);
      expect(state.list).toEqual(users);
    });
  });
});
```

## Component Testing

### 1. Testing Presentational Components
```javascript
// shared/components/Button/Button.spec.js
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import Button from './Button';

describe('Button', () => {
  it('should render with correct text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });

  it('should handle click events', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    
    fireEvent.click(screen.getByText('Click me'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('should be disabled when disabled prop is true', () => {
    render(<Button disabled>Click me</Button>);
    expect(screen.getByText('Click me')).toBeDisabled();
  });

  it('should apply correct variant class', () => {
    render(<Button variant="secondary">Click me</Button>);
    expect(screen.getByText('Click me')).toHaveClass('button--secondary');
  });
});
```

### 2. Testing Components with Redux
```javascript
// features/User/components/UserList/UserList.spec.js
import React from 'react';
import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import UserList from './UserList';
import userReducer from '../../state/userSlice';

const createMockStore = (initialState) => {
  return configureStore({
    reducer: {
      users: userReducer,
    },
    preloadedState: initialState,
  });
};

describe('UserList', () => {
  it('should display loading state', () => {
    const store = createMockStore({
      users: { list: [], loading: true, error: null },
    });

    render(
      <Provider store={store}>
        <UserList />
      </Provider>
    );

    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('should display users when loaded', () => {
    const mockUsers = [
      { id: 1, name: 'John Doe', email: '<EMAIL>' },
      { id: 2, name: 'Jane Smith', email: '<EMAIL>' },
    ];

    const store = createMockStore({
      users: { list: mockUsers, loading: false, error: null },
    });

    render(
      <Provider store={store}>
        <UserList />
      </Provider>
    );

    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('Jane Smith')).toBeInTheDocument();
  });
});
```

### 3. Testing Custom Hooks
```javascript
// features/User/hooks/useUserData.spec.js
import { renderHook, act } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import useUserData from './useUserData';
import userReducer from '../state/userSlice';

const wrapper = ({ children }) => {
  const store = configureStore({
    reducer: { users: userReducer },
  });
  return <Provider store={store}>{children}</Provider>;
};

describe('useUserData', () => {
  it('should return initial state', () => {
    const { result } = renderHook(() => useUserData(), { wrapper });

    expect(result.current.users).toEqual([]);
    expect(result.current.loading).toBe(false);
  });

  it('should fetch users on mount', async () => {
    const { result } = renderHook(() => useUserData(), { wrapper });

    await act(async () => {
      result.current.fetchUsers();
    });

    expect(result.current.loading).toBe(true);
  });
});
```

## Integration Testing

### 1. Testing Component Interactions
```javascript
// __tests__/integration/UserManagement.spec.js
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { store } from 'core/store';
import UserManagementPage from 'pages/UserManagement';

const renderWithProviders = (component) => {
  return render(
    <Provider store={store}>
      <BrowserRouter>
        {component}
      </BrowserRouter>
    </Provider>
  );
};

describe('User Management Integration', () => {
  it('should create a new user', async () => {
    renderWithProviders(<UserManagementPage />);

    // Click add user button
    fireEvent.click(screen.getByText('Add User'));

    // Fill form
    fireEvent.change(screen.getByLabelText('Name'), {
      target: { value: 'John Doe' },
    });
    fireEvent.change(screen.getByLabelText('Email'), {
      target: { value: '<EMAIL>' },
    });

    // Submit form
    fireEvent.click(screen.getByText('Save'));

    // Wait for user to appear in list
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });
  });
});
```

### 2. API Integration Testing
```javascript
// __tests__/integration/api.spec.js
import { userService } from 'features/User/services/userService';
import { server } from '../__mocks__/server';

describe('API Integration', () => {
  beforeAll(() => server.listen());
  afterEach(() => server.resetHandlers());
  afterAll(() => server.close());

  it('should fetch users from API', async () => {
    const users = await userService.getUsers();
    
    expect(users.data).toHaveLength(2);
    expect(users.data[0]).toHaveProperty('id');
    expect(users.data[0]).toHaveProperty('name');
  });
});
```

## Testing Utilities

### 1. Custom Render Function
```javascript
// __tests__/utils/testUtils.js
import React from 'react';
import { render } from '@testing-library/react';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import { configureStore } from '@reduxjs/toolkit';
import theme from '@nv2/nv2-pkg-js-theme/src/components/theme/theme';
import { rootReducer } from 'core/rootReducer';

export const renderWithProviders = (
  ui,
  {
    preloadedState = {},
    store = configureStore({
      reducer: rootReducer,
      preloadedState,
    }),
    ...renderOptions
  } = {}
) => {
  const Wrapper = ({ children }) => (
    <Provider store={store}>
      <BrowserRouter>
        <ThemeProvider theme={theme}>
          {children}
        </ThemeProvider>
      </BrowserRouter>
    </Provider>
  );

  return { store, ...render(ui, { wrapper: Wrapper, ...renderOptions }) };
};
```

### 2. Mock Data Factories
```javascript
// __mocks__/factories/userFactory.js
export const createMockUser = (overrides = {}) => ({
  id: 1,
  name: 'John Doe',
  email: '<EMAIL>',
  role: 'user',
  isActive: true,
  createdAt: '2023-01-01T00:00:00Z',
  ...overrides,
});

export const createMockUsers = (count = 3) => {
  return Array.from({ length: count }, (_, index) =>
    createMockUser({
      id: index + 1,
      name: `User ${index + 1}`,
      email: `user${index + 1}@example.com`,
    })
  );
};
```

## Best Practices

### 1. Test Organization
- Group related tests using `describe` blocks
- Use descriptive test names that explain the expected behavior
- Follow the AAA pattern: Arrange, Act, Assert
- Keep tests focused on a single behavior

### 2. Mocking Guidelines
- Mock external dependencies (APIs, third-party libraries)
- Don't mock the code you're testing
- Use factory functions for creating test data
- Reset mocks between tests

### 3. Async Testing
- Use `waitFor` for async operations
- Test loading states and error states
- Use `act` when updating state in tests

### 4. Accessibility Testing
```javascript
import { axe, toHaveNoViolations } from 'jest-axe';

expect.extend(toHaveNoViolations);

it('should not have accessibility violations', async () => {
  const { container } = render(<MyComponent />);
  const results = await axe(container);
  expect(results).toHaveNoViolations();
});
```

### 5. Coverage Guidelines
- Aim for 80%+ code coverage
- Focus on critical business logic
- Don't sacrifice test quality for coverage numbers
- Use coverage reports to identify untested code paths

## Testing Checklist

Before submitting code:
- [ ] All tests pass
- [ ] New features have corresponding tests
- [ ] Edge cases are tested
- [ ] Error states are tested
- [ ] Async operations are properly tested
- [ ] Mocks are properly configured
- [ ] Tests are readable and maintainable
- [ ] Coverage meets project standards
