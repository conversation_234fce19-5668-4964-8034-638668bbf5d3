# Coding Guidelines - NGA UI

## Table of Contents
- [General Principles](#general-principles)
- [JavaScript/React Standards](#javascriptreact-standards)
- [Code Organization](#code-organization)
- [Naming Conventions](#naming-conventions)
- [Error Handling](#error-handling)
- [Performance Guidelines](#performance-guidelines)
- [Code Comments](#code-comments)

## General Principles

### 1. Code Quality
- Write clean, readable, and maintainable code
- Follow the DRY (Don't Repeat Yourself) principle
- Use meaningful variable and function names
- Keep functions small and focused on a single responsibility
- Prefer composition over inheritance

### 2. Consistency
- Follow established patterns within the codebase
- Use consistent formatting (handled by ESLint/Prettier)
- Maintain consistent file and folder structure
- Use consistent naming conventions across the project

## JavaScript/React Standards

### 1. ESLint Configuration
The project uses Airbnb ESLint configuration with custom rules. Always ensure your code passes linting:

```bash
npm run lint
```

### 2. Function Components
- Use arrow function components for consistency
- Prefer functional components over class components
- Use React Hooks for state management and side effects

```javascript
// ✅ Good
const MyComponent = ({ prop1, prop2 }) => {
  const [state, setState] = useState(initialValue);
  
  useEffect(() => {
    // Side effects here
  }, []);

  return <div>{/* JSX */}</div>;
};

// ❌ Avoid
function MyComponent(props) {
  // Function declaration style
}
```

### 3. Props and PropTypes
- Always define PropTypes for components
- Use destructuring for props
- Provide default values when appropriate

```javascript
import PropTypes from 'prop-types';

const MyComponent = ({ title, isVisible = false, onAction }) => {
  // Component logic
};

MyComponent.propTypes = {
  title: PropTypes.string.isRequired,
  isVisible: PropTypes.bool,
  onAction: PropTypes.func.isRequired,
};
```

### 4. State Management
- Use Redux Toolkit for global state
- Use local state (useState) for component-specific state
- Use useSelector and useDispatch hooks for Redux integration

```javascript
// ✅ Good - Redux Toolkit slice
import { createSlice } from '@reduxjs/toolkit';

const userSlice = createSlice({
  name: 'user',
  initialState: { data: null, loading: false },
  reducers: {
    setUser: (state, action) => {
      state.data = action.payload;
    },
  },
});
```

### 5. Async Operations
- Use async/await instead of .then() chains
- Handle errors properly with try/catch blocks
- Use Redux Toolkit Query or custom async thunks for API calls

```javascript
// ✅ Good
const fetchUserData = async (userId) => {
  try {
    const response = await userService.getUser(userId);
    return response.data;
  } catch (error) {
    console.error('Failed to fetch user:', error);
    throw error;
  }
};
```

## Code Organization

### 1. Import Order
Follow this import order (enforced by ESLint):

```javascript
// 1. React and external libraries
import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Button } from '@mui/material';

// 2. Internal modules (core, features, etc.)
import { HTTPService } from 'core/services';
import { getUserData } from 'features/User/actions';

// 3. Relative imports
import './Component.scss';
```

### 2. File Structure
- Keep files focused and under 300 lines when possible
- Separate concerns into different files
- Use index.js files for clean imports

### 3. Folder Organization
```
src/
├── core/           # Core utilities, services, state
├── features/       # Feature-specific components and logic
├── pages/          # Page components
├── shared/         # Reusable components
├── layouts/        # Layout components
└── assets/         # Static assets
```

## Naming Conventions

### 1. Files and Folders
- Use PascalCase for component files: `UserProfile.jsx`
- Use camelCase for utility files: `dateUtils.js`
- Use kebab-case for folders: `user-profile/`
- Use UPPER_CASE for constants: `API_ENDPOINTS.js`

### 2. Variables and Functions
- Use camelCase: `userName`, `fetchUserData`
- Use descriptive names: `isLoading` instead of `loading`
- Use verb-noun pattern for functions: `getUserById`, `updateUserProfile`

### 3. Components
- Use PascalCase: `UserProfile`, `NavigationBar`
- Use descriptive names that indicate purpose
- Prefix with adjectives when needed: `PrimaryButton`, `SecondaryButton`

### 4. Constants
- Use UPPER_SNAKE_CASE: `API_BASE_URL`, `MAX_RETRY_ATTEMPTS`
- Group related constants in objects or enums

```javascript
const API_ENDPOINTS = {
  USERS: '/api/users',
  AGREEMENTS: '/api/agreements',
};

const USER_ROLES = {
  ADMIN: 'admin',
  USER: 'user',
  VIEWER: 'viewer',
};
```

## Error Handling

### 1. Component Error Boundaries
- Use error boundaries for critical components
- Provide fallback UI for errors
- Log errors appropriately

### 2. API Error Handling
- Handle different types of errors (network, validation, server)
- Provide user-friendly error messages
- Use consistent error response format

```javascript
const handleApiError = (error) => {
  if (error.response?.status === 401) {
    // Handle unauthorized
    redirectToLogin();
  } else if (error.response?.status >= 500) {
    // Handle server errors
    showErrorToast('Server error occurred');
  } else {
    // Handle other errors
    showErrorToast(error.message || 'An error occurred');
  }
};
```

## Performance Guidelines

### 1. React Performance
- Use React.memo for expensive components
- Use useMemo and useCallback for expensive calculations
- Avoid creating objects/functions in render

```javascript
// ✅ Good
const ExpensiveComponent = React.memo(({ data, onAction }) => {
  const processedData = useMemo(() => 
    expensiveCalculation(data), [data]
  );
  
  const handleClick = useCallback(() => {
    onAction(data.id);
  }, [data.id, onAction]);

  return <div onClick={handleClick}>{processedData}</div>;
});
```

### 2. Bundle Optimization
- Use dynamic imports for code splitting
- Lazy load components when appropriate
- Optimize images and assets

## Code Comments

### 1. When to Comment
- Complex business logic
- Non-obvious code decisions
- API integrations
- Temporary workarounds (with TODO/FIXME)

### 2. Comment Style
```javascript
/**
 * Calculates the discount amount based on user tier and purchase amount
 * @param {number} amount - Purchase amount
 * @param {string} userTier - User tier (premium, standard, basic)
 * @returns {number} Discount amount
 */
const calculateDiscount = (amount, userTier) => {
  // Apply tier-based discount rates
  const discountRates = {
    premium: 0.15,
    standard: 0.10,
    basic: 0.05,
  };
  
  return amount * (discountRates[userTier] || 0);
};
```

### 3. JSDoc for Functions
- Document public functions and methods
- Include parameter types and return types
- Provide usage examples for complex functions

## Code Review Checklist

Before submitting code for review:
- [ ] Code passes all linting rules
- [ ] All tests pass
- [ ] PropTypes are defined for React components
- [ ] Error handling is implemented
- [ ] Performance considerations are addressed
- [ ] Code is properly commented
- [ ] Naming conventions are followed
- [ ] No console.log statements in production code
