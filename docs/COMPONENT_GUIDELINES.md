# React Component Guidelines - NGA UI

## Table of Contents
- [Component Structure](#component-structure)
- [Component Types](#component-types)
- [Props and State](#props-and-state)
- [Hooks Usage](#hooks-usage)
- [Styling Guidelines](#styling-guidelines)
- [Component Testing](#component-testing)
- [Performance Optimization](#performance-optimization)

## Component Structure

### 1. File Organization
Each component should follow this structure:

```
ComponentName/
├── index.js          # Export file
├── ComponentName.jsx # Main component
├── ComponentName.scss # Styles
├── ComponentName.spec.js # Tests
└── hooks/            # Component-specific hooks (if needed)
    └── useComponentName.js
```

### 2. Component Template
```javascript
import React, { useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import { useSelector, useDispatch } from 'react-redux';
import { Button } from '@mui/material';

import { someAction } from 'core/state/actions';
import { SomeService } from 'core/services';

import './ComponentName.scss';

const ComponentName = ({ 
  prop1, 
  prop2 = 'defaultValue', 
  onAction,
  children 
}) => {
  // 1. Hooks (useState, useEffect, custom hooks)
  const [localState, setLocalState] = useState(null);
  const dispatch = useDispatch();
  const storeData = useSelector(state => state.someSlice.data);

  // 2. Event handlers
  const handleClick = useCallback(() => {
    onAction?.(localState);
  }, [localState, onAction]);

  // 3. Effects
  useEffect(() => {
    // Side effects
  }, []);

  // 4. Render helpers (if needed)
  const renderContent = () => {
    if (!storeData) return <div>Loading...</div>;
    return <div>{storeData.title}</div>;
  };

  // 5. Main render
  return (
    <div className="component-name">
      {renderContent()}
      <Button onClick={handleClick}>
        {prop1}
      </Button>
      {children}
    </div>
  );
};

ComponentName.propTypes = {
  prop1: PropTypes.string.isRequired,
  prop2: PropTypes.string,
  onAction: PropTypes.func,
  children: PropTypes.node,
};

export default ComponentName;
```

## Component Types

### 1. Presentational Components
- Focus on how things look
- Receive data and callbacks via props
- Rarely have their own state
- Don't know about Redux

```javascript
const UserCard = ({ user, onEdit, onDelete }) => (
  <div className="user-card">
    <h3>{user.name}</h3>
    <p>{user.email}</p>
    <Button onClick={() => onEdit(user.id)}>Edit</Button>
    <Button onClick={() => onDelete(user.id)}>Delete</Button>
  </div>
);
```

### 2. Container Components
- Focus on how things work
- Connect to Redux store
- Handle data fetching and state updates
- Pass data to presentational components

```javascript
const UserListContainer = () => {
  const dispatch = useDispatch();
  const users = useSelector(state => state.users.list);
  
  const handleEditUser = useCallback((userId) => {
    dispatch(editUser(userId));
  }, [dispatch]);

  return (
    <div>
      {users.map(user => (
        <UserCard 
          key={user.id}
          user={user}
          onEdit={handleEditUser}
        />
      ))}
    </div>
  );
};
```

### 3. Higher-Order Components (HOCs)
Use sparingly, prefer hooks when possible:

```javascript
const withLoading = (WrappedComponent) => {
  return ({ isLoading, ...props }) => {
    if (isLoading) {
      return <div>Loading...</div>;
    }
    return <WrappedComponent {...props} />;
  };
};
```

## Props and State

### 1. Props Guidelines
- Use destructuring in function parameters
- Provide default values for optional props
- Use PropTypes for type checking
- Keep prop names descriptive and consistent

```javascript
// ✅ Good
const Button = ({ 
  variant = 'primary', 
  size = 'medium', 
  disabled = false,
  onClick,
  children 
}) => {
  // Component logic
};

Button.propTypes = {
  variant: PropTypes.oneOf(['primary', 'secondary', 'danger']),
  size: PropTypes.oneOf(['small', 'medium', 'large']),
  disabled: PropTypes.bool,
  onClick: PropTypes.func.isRequired,
  children: PropTypes.node.isRequired,
};
```

### 2. State Management
- Use local state for UI-specific data
- Use Redux for shared application state
- Keep state as flat as possible
- Use functional updates when state depends on previous state

```javascript
// ✅ Good - Local UI state
const [formData, setFormData] = useState({
  name: '',
  email: '',
});

const updateFormField = (field, value) => {
  setFormData(prev => ({
    ...prev,
    [field]: value
  }));
};

// ✅ Good - Functional update
const [count, setCount] = useState(0);
const increment = () => setCount(prev => prev + 1);
```

## Hooks Usage

### 1. Built-in Hooks
- Use hooks at the top level of components
- Don't call hooks inside loops, conditions, or nested functions
- Use dependency arrays correctly in useEffect and useCallback

```javascript
// ✅ Good
const MyComponent = ({ userId }) => {
  const [user, setUser] = useState(null);
  
  useEffect(() => {
    if (userId) {
      fetchUser(userId).then(setUser);
    }
  }, [userId]); // Correct dependency

  const handleUpdate = useCallback((data) => {
    updateUser(userId, data);
  }, [userId]); // Correct dependency

  return <div>{user?.name}</div>;
};
```

### 2. Custom Hooks
- Extract reusable logic into custom hooks
- Follow the "use" naming convention
- Return objects for multiple values, arrays for ordered values

```javascript
// Custom hook for API data fetching
const useApiData = (url) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const response = await fetch(url);
        const result = await response.json();
        setData(result);
      } catch (err) {
        setError(err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [url]);

  return { data, loading, error };
};

// Usage
const MyComponent = () => {
  const { data, loading, error } = useApiData('/api/users');
  
  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  
  return <div>{data?.map(user => user.name)}</div>;
};
```

## Styling Guidelines

### 1. SCSS Modules
- Use SCSS modules for component-specific styles
- Follow BEM naming convention
- Use CSS custom properties for theming

```scss
// ComponentName.scss
.component-name {
  padding: 1rem;
  background-color: var(--background-color);

  &__header {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
  }

  &__content {
    line-height: 1.6;
  }

  &--highlighted {
    border: 2px solid var(--primary-color);
  }
}
```

### 2. Material-UI Integration
- Use Material-UI components as base
- Customize with theme and sx prop when needed
- Create reusable styled components

```javascript
import { styled } from '@mui/material/styles';
import { Button } from '@mui/material';

const StyledButton = styled(Button)(({ theme }) => ({
  borderRadius: theme.spacing(1),
  textTransform: 'none',
  fontWeight: 600,
}));

const MyComponent = () => (
  <StyledButton variant="contained" color="primary">
    Custom Button
  </StyledButton>
);
```

## Component Testing

### 1. Test Structure
```javascript
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';

import ComponentName from './ComponentName';

const mockStore = configureStore({
  reducer: {
    // Mock reducers
  },
});

const renderWithProvider = (component) => {
  return render(
    <Provider store={mockStore}>
      {component}
    </Provider>
  );
};

describe('ComponentName', () => {
  it('renders correctly', () => {
    renderWithProvider(<ComponentName prop1="test" />);
    expect(screen.getByText('test')).toBeInTheDocument();
  });

  it('handles click events', () => {
    const mockOnAction = jest.fn();
    renderWithProvider(
      <ComponentName prop1="test" onAction={mockOnAction} />
    );
    
    fireEvent.click(screen.getByRole('button'));
    expect(mockOnAction).toHaveBeenCalled();
  });
});
```

### 2. Testing Best Practices
- Test behavior, not implementation
- Use data-testid for complex queries
- Mock external dependencies
- Test error states and edge cases

## Performance Optimization

### 1. React.memo
Use for components that re-render frequently with same props:

```javascript
const ExpensiveComponent = React.memo(({ data, onAction }) => {
  // Expensive rendering logic
  return <div>{/* Complex JSX */}</div>;
});

// Custom comparison function if needed
const areEqual = (prevProps, nextProps) => {
  return prevProps.data.id === nextProps.data.id;
};

const OptimizedComponent = React.memo(MyComponent, areEqual);
```

### 2. useMemo and useCallback
```javascript
const MyComponent = ({ items, filter, onItemClick }) => {
  // Memoize expensive calculations
  const filteredItems = useMemo(() => {
    return items.filter(item => item.category === filter);
  }, [items, filter]);

  // Memoize event handlers
  const handleItemClick = useCallback((itemId) => {
    onItemClick(itemId);
  }, [onItemClick]);

  return (
    <div>
      {filteredItems.map(item => (
        <Item 
          key={item.id} 
          item={item} 
          onClick={handleItemClick} 
        />
      ))}
    </div>
  );
};
```

### 3. Code Splitting
```javascript
import { lazy, Suspense } from 'react';

const LazyComponent = lazy(() => import('./LazyComponent'));

const App = () => (
  <Suspense fallback={<div>Loading...</div>}>
    <LazyComponent />
  </Suspense>
);
```

## Component Checklist

Before submitting a component:
- [ ] PropTypes are defined
- [ ] Default props are provided where appropriate
- [ ] Component is properly tested
- [ ] Styles follow SCSS guidelines
- [ ] Performance optimizations are considered
- [ ] Error boundaries are implemented if needed
- [ ] Accessibility attributes are included
- [ ] Component is documented with JSDoc if complex
