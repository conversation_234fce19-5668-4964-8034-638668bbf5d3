# Git Workflow Guidelines - NGA UI

## Table of Contents
- [Branching Strategy](#branching-strategy)
- [Commit Message Standards](#commit-message-standards)
- [Pull Request Process](#pull-request-process)
- [Code Review Guidelines](#code-review-guidelines)
- [Git Hooks](#git-hooks)
- [Release Process](#release-process)

## Branching Strategy

### 1. Branch Types
We follow a Git Flow-inspired branching strategy:

```
main (production)
├── develop (integration)
│   ├── feature/NGA-123-user-management
│   ├── feature/NGA-124-budget-calculation
│   └── bugfix/NGA-125-fix-login-issue
├── hotfix/NGA-126-critical-security-fix
└── release/v1.2.0
```

### 2. Branch Naming Convention
- **Feature branches**: `feature/TICKET-ID-short-description`
- **Bug fix branches**: `bugfix/TICKET-ID-short-description`
- **Hotfix branches**: `hotfix/TICKET-ID-short-description`
- **Release branches**: `release/vX.Y.Z`

Examples:
```bash
feature/NGA-123-user-profile-page
bugfix/NGA-124-fix-date-picker
hotfix/NGA-125-security-vulnerability
release/v1.2.0
```

### 3. Branch Lifecycle

#### Feature Development
```bash
# Create feature branch from develop
git checkout develop
git pull origin develop
git checkout -b feature/NGA-123-user-management

# Work on feature
git add .
git commit -m "feat(user): add user profile component"

# Push and create PR
git push origin feature/NGA-123-user-management
```

#### Bug Fixes
```bash
# Create bugfix branch from develop
git checkout develop
git pull origin develop
git checkout -b bugfix/NGA-124-fix-validation

# Fix bug and commit
git add .
git commit -m "fix(validation): resolve email validation issue"

# Push and create PR
git push origin bugfix/NGA-124-fix-validation
```

#### Hotfixes
```bash
# Create hotfix branch from main
git checkout main
git pull origin main
git checkout -b hotfix/NGA-125-critical-fix

# Apply fix
git add .
git commit -m "fix(security): patch XSS vulnerability"

# Push and create PR to main
git push origin hotfix/NGA-125-critical-fix
```

## Commit Message Standards

### 1. Commit Message Format
We follow the Conventional Commits specification:

```
<type>(<scope>): <description>

[optional body]

[optional footer(s)]
```

### 2. Commit Types
- **feat**: New feature
- **fix**: Bug fix
- **docs**: Documentation changes
- **style**: Code style changes (formatting, missing semicolons, etc.)
- **refactor**: Code refactoring without changing functionality
- **perf**: Performance improvements
- **test**: Adding or updating tests
- **chore**: Maintenance tasks, dependency updates
- **ci**: CI/CD configuration changes

### 3. Scope Examples
- **user**: User management features
- **budget**: Budget-related functionality
- **agreement**: Agreement management
- **auth**: Authentication and authorization
- **ui**: UI components and styling
- **api**: API integration
- **config**: Configuration changes

### 4. Commit Message Examples

```bash
# Feature commits
feat(user): add user profile editing functionality
feat(budget): implement budget calculation engine
feat(ui): add responsive navigation component

# Bug fix commits
fix(auth): resolve token refresh issue
fix(validation): fix email format validation
fix(ui): correct button alignment on mobile

# Documentation commits
docs(readme): update installation instructions
docs(api): add API endpoint documentation

# Refactoring commits
refactor(user): extract user validation logic
refactor(components): simplify button component structure

# Test commits
test(user): add unit tests for user service
test(integration): add budget calculation integration tests

# Chore commits
chore(deps): update React to version 18.2.0
chore(config): update ESLint configuration
```

### 5. Commit Message Rules
- Use imperative mood ("add" not "added" or "adds")
- Keep the first line under 72 characters
- Capitalize the first letter of the description
- Don't end the description with a period
- Use the body to explain what and why, not how
- Reference issue numbers in the footer

```bash
# Good commit message
feat(budget): add budget comparison feature

Implement side-by-side budget comparison with visual indicators
for variance analysis. Users can now compare up to 3 budgets
simultaneously with filtering options.

Closes #123
```

## Pull Request Process

### 1. PR Title Format
Use the same format as commit messages:
```
feat(user): add user profile editing functionality
fix(auth): resolve token refresh issue
```

### 2. PR Description Template
```markdown
## Description
Brief description of changes made.

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed

## Screenshots (if applicable)
Add screenshots for UI changes.

## Checklist
- [ ] Code follows project style guidelines
- [ ] Self-review completed
- [ ] Code is properly commented
- [ ] Tests added/updated
- [ ] Documentation updated
- [ ] No console.log statements left in code

## Related Issues
Closes #123
```

### 3. PR Requirements
Before creating a PR:
- [ ] Branch is up to date with target branch
- [ ] All tests pass locally
- [ ] Code passes linting checks
- [ ] No merge conflicts
- [ ] PR has descriptive title and description
- [ ] Appropriate reviewers assigned

## Code Review Guidelines

### 1. Review Checklist
**Functionality**
- [ ] Code works as intended
- [ ] Edge cases are handled
- [ ] Error handling is appropriate
- [ ] Performance considerations addressed

**Code Quality**
- [ ] Code is readable and maintainable
- [ ] Follows project conventions
- [ ] No code duplication
- [ ] Proper separation of concerns

**Testing**
- [ ] Adequate test coverage
- [ ] Tests are meaningful
- [ ] Tests pass consistently

**Security**
- [ ] No security vulnerabilities
- [ ] Input validation implemented
- [ ] Authentication/authorization handled

### 2. Review Process
1. **Author**: Create PR with detailed description
2. **Reviewers**: Review code within 24 hours
3. **Author**: Address feedback and update PR
4. **Reviewers**: Re-review and approve
5. **Author**: Merge after all approvals

### 3. Review Comments
Use constructive feedback:

```markdown
# Good feedback
Consider extracting this logic into a separate function for better reusability.

# Better feedback with suggestion
Consider extracting this logic into a separate function for better reusability:

```javascript
const validateUserInput = (input) => {
  // validation logic here
};
```

# Nitpick (optional changes)
Nit: Consider using a more descriptive variable name here.
```

## Git Hooks

### 1. Pre-commit Hook
Automatically runs linting and formatting:

```bash
#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

npm run lint && npm run stylelint
```

### 2. Pre-push Hook
Runs tests before pushing:

```bash
#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

npm run test:local
```

### 3. Commit Message Hook
Validates commit message format:

```bash
#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

npx commitlint --edit $1
```

## Release Process

### 1. Version Numbering
Follow Semantic Versioning (SemVer):
- **MAJOR**: Breaking changes
- **MINOR**: New features (backward compatible)
- **PATCH**: Bug fixes (backward compatible)

### 2. Release Branch Workflow
```bash
# Create release branch from develop
git checkout develop
git pull origin develop
git checkout -b release/v1.2.0

# Update version numbers and changelog
npm version minor
git add .
git commit -m "chore(release): bump version to 1.2.0"

# Push release branch
git push origin release/v1.2.0

# Create PR to main
# After approval and merge, tag the release
git checkout main
git pull origin main
git tag -a v1.2.0 -m "Release version 1.2.0"
git push origin v1.2.0

# Merge back to develop
git checkout develop
git merge main
git push origin develop
```

### 3. Changelog Management
Maintain a CHANGELOG.md file:

```markdown
# Changelog

## [1.2.0] - 2023-12-01

### Added
- User profile editing functionality
- Budget comparison feature
- Export functionality for reports

### Fixed
- Token refresh issue in authentication
- Date picker validation bug
- Mobile responsive layout issues

### Changed
- Updated Material-UI to version 5.10
- Improved error handling in API calls

### Deprecated
- Legacy user management API endpoints

### Removed
- Unused utility functions
- Deprecated component variants
```

## Git Best Practices

### 1. General Guidelines
- Commit early and often
- Write meaningful commit messages
- Keep commits atomic (one logical change per commit)
- Use branches for all development work
- Never commit directly to main or develop
- Always create PRs for code changes

### 2. Merge vs Rebase
- Use **merge** for feature branches to preserve history
- Use **rebase** for cleaning up local commits before pushing
- Use **squash merge** for small features or bug fixes

### 3. Conflict Resolution
```bash
# When conflicts occur during merge
git status
# Edit conflicted files
git add .
git commit -m "resolve merge conflicts"

# When conflicts occur during rebase
git status
# Edit conflicted files
git add .
git rebase --continue
```

## Workflow Checklist

Before starting work:
- [ ] Pull latest changes from develop
- [ ] Create feature branch with proper naming
- [ ] Set up tracking branch

During development:
- [ ] Make atomic commits with good messages
- [ ] Push regularly to backup work
- [ ] Keep branch up to date with develop

Before creating PR:
- [ ] Rebase/merge latest develop
- [ ] Run tests locally
- [ ] Run linting checks
- [ ] Write descriptive PR description

After PR approval:
- [ ] Merge using appropriate strategy
- [ ] Delete feature branch
- [ ] Update local develop branch
