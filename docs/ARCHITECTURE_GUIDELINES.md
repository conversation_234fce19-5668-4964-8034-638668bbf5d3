# Architecture Guidelines - NGA UI

## Table of Contents
- [Project Structure](#project-structure)
- [Module Federation](#module-federation)
- [State Management](#state-management)
- [Service Layer](#service-layer)
- [Routing](#routing)
- [Configuration Management](#configuration-management)
- [Dependency Management](#dependency-management)

## Project Structure

### 1. Folder Organization
The project follows a feature-based architecture with clear separation of concerns:

```
src/
├── core/                    # Core application logic
│   ├── configs/            # Configuration files
│   ├── hooks/              # Shared custom hooks
│   ├── interceptors/       # HTTP interceptors
│   ├── services/           # API services and utilities
│   ├── state/              # Redux store configuration
│   └── utilities/          # Utility functions
├── features/               # Feature-specific modules
│   ├── FeatureName/
│   │   ├── components/     # Feature components
│   │   ├── hooks/          # Feature-specific hooks
│   │   ├── services/       # Feature-specific services
│   │   └── state/          # Feature-specific state
├── pages/                  # Page-level components
├── shared/                 # Reusable components
├── layouts/                # Layout components
└── assets/                 # Static assets
```

### 2. Feature Module Structure
Each feature should be self-contained:

```
features/UserManagement/
├── index.js                # Feature exports
├── components/
│   ├── UserList/
│   ├── UserForm/
│   └── UserCard/
├── hooks/
│   ├── useUserData.js
│   └── useUserValidation.js
├── services/
│   └── userService.js
├── state/
│   ├── userSlice.js
│   └── userSelectors.js
└── constants/
    └── userConstants.js
```

### 3. Import Path Aliases
Use configured path aliases for clean imports:

```javascript
// ✅ Good - Using aliases
import { HTTPService } from 'core/services';
import { UserCard } from 'features/UserManagement';
import { Button } from 'shared/components';

// ❌ Avoid - Relative paths
import { HTTPService } from '../../../core/services';
```

## Module Federation

### 1. Micro-Frontend Architecture
The application is configured as a micro-frontend using Webpack Module Federation:

```javascript
// craco.config.js - Module Federation setup
new ModuleFederationPlugin({
  name: 'analytics',
  filename: 'remoteEntry.js',
  exposes: {
    './Analytics': './src/AppProvider.jsx',
  },
  shared: {
    react: { eager: true },
    'react-dom': { eager: true },
    // Other shared dependencies
  },
});
```

### 2. Exposed Components
- Components exposed via Module Federation should be stable APIs
- Maintain backward compatibility when updating exposed components
- Document exposed component interfaces

### 3. Shared Dependencies
- Keep shared dependencies up to date across micro-frontends
- Use semantic versioning for shared packages
- Test integration between micro-frontends regularly

## State Management

### 1. Redux Toolkit Structure
Use Redux Toolkit for global state management:

```javascript
// store.js
import { configureStore } from '@reduxjs/toolkit';
import { rootReducer } from './rootReducer';

export const store = configureStore({
  reducer: rootReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
});
```

### 2. Slice Organization
Create feature-specific slices:

```javascript
// features/User/state/userSlice.js
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { userService } from '../services/userService';

export const fetchUsers = createAsyncThunk(
  'users/fetchUsers',
  async (params, { rejectWithValue }) => {
    try {
      const response = await userService.getUsers(params);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data);
    }
  }
);

const userSlice = createSlice({
  name: 'users',
  initialState: {
    list: [],
    loading: false,
    error: null,
  },
  reducers: {
    clearUsers: (state) => {
      state.list = [];
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchUsers.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchUsers.fulfilled, (state, action) => {
        state.loading = false;
        state.list = action.payload;
      })
      .addCase(fetchUsers.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { clearUsers } = userSlice.actions;
export default userSlice.reducer;
```

### 3. Selectors
Create reusable selectors:

```javascript
// features/User/state/userSelectors.js
import { createSelector } from '@reduxjs/toolkit';

const selectUserState = (state) => state.users;

export const selectUsers = createSelector(
  [selectUserState],
  (userState) => userState.list
);

export const selectActiveUsers = createSelector(
  [selectUsers],
  (users) => users.filter(user => user.isActive)
);

export const selectUserById = createSelector(
  [selectUsers, (state, userId) => userId],
  (users, userId) => users.find(user => user.id === userId)
);
```

## Service Layer

### 1. HTTP Service
Centralized HTTP service with interceptors:

```javascript
// core/services/HTTPService.js
import axios from 'axios';

class HTTPService {
  constructor() {
    this.client = axios.create({
      baseURL: process.env.REACT_APP_API_URL,
      timeout: 10000,
    });

    this.setupInterceptors();
  }

  setupInterceptors() {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('authToken');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Handle unauthorized
          this.handleUnauthorized();
        }
        return Promise.reject(error);
      }
    );
  }

  async get(url, config = {}) {
    return this.client.get(url, config);
  }

  async post(url, data, config = {}) {
    return this.client.post(url, data, config);
  }

  // Other HTTP methods...
}

export const httpService = new HTTPService();
```

### 2. Feature Services
Create feature-specific services:

```javascript
// features/User/services/userService.js
import { httpService } from 'core/services/HTTPService';

class UserService {
  async getUsers(params = {}) {
    return httpService.get('/users', { params });
  }

  async getUserById(id) {
    return httpService.get(`/users/${id}`);
  }

  async createUser(userData) {
    return httpService.post('/users', userData);
  }

  async updateUser(id, userData) {
    return httpService.put(`/users/${id}`, userData);
  }

  async deleteUser(id) {
    return httpService.delete(`/users/${id}`);
  }
}

export const userService = new UserService();
```

## Routing

### 1. Route Configuration
Centralized route configuration:

```javascript
// core/configs/routes.js
export const ROUTES = {
  HOME: '/',
  USERS: '/users',
  USER_DETAILS: '/users/:id',
  AGREEMENTS: '/agreements',
  BUDGET_LIST: '/budgets',
  BUDGET_DETAILS: '/budgets/:id',
};

// Route components
export const routeConfig = [
  {
    path: ROUTES.HOME,
    component: HomePage,
    exact: true,
  },
  {
    path: ROUTES.USERS,
    component: UsersPage,
    exact: true,
  },
  {
    path: ROUTES.USER_DETAILS,
    component: UserDetailsPage,
  },
];
```

### 2. Protected Routes
Implement route protection:

```javascript
// shared/components/ProtectedRoute.jsx
import { Navigate } from 'react-router-dom';
import { useSelector } from 'react-redux';

const ProtectedRoute = ({ children, requiredRole }) => {
  const { isAuthenticated, user } = useSelector(state => state.auth);

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  if (requiredRole && user.role !== requiredRole) {
    return <Navigate to="/unauthorized" replace />;
  }

  return children;
};
```

## Configuration Management

### 1. Environment Configuration
Use environment-specific configurations:

```javascript
// core/configs/config.js
const config = {
  development: {
    apiUrl: process.env.REACT_APP_API_URL || 'http://localhost:3001',
    logLevel: 'debug',
  },
  production: {
    apiUrl: process.env.REACT_APP_API_URL,
    logLevel: 'error',
  },
};

export default config[process.env.NODE_ENV];
```

### 2. Runtime Configuration
Load configuration at runtime:

```javascript
// core/services/ConfigService.js
class ConfigService {
  constructor() {
    this.config = null;
  }

  async loadConfig() {
    try {
      const response = await fetch('/settings/config.json');
      this.config = await response.json();
      return this.config;
    } catch (error) {
      console.error('Failed to load configuration:', error);
      throw error;
    }
  }

  get(key, defaultValue = null) {
    return this.config?.[key] ?? defaultValue;
  }
}

export const configService = new ConfigService();
```

## Dependency Management

### 1. Package Organization
- Keep dependencies up to date
- Use exact versions for critical packages
- Separate dev dependencies from production dependencies
- Document any peer dependency requirements

### 2. Private Package Integration
Handle private @nv2 packages:

```javascript
// Proper import from private packages
import { Toastr } from '@nv2/nv2-pkg-js-shared-components/lib/Toastr';
import theme from '@nv2/nv2-pkg-js-theme/src/components/theme/theme';
```

### 3. Bundle Optimization
- Use dynamic imports for code splitting
- Analyze bundle size regularly
- Optimize shared dependencies in Module Federation

## Architecture Principles

### 1. Separation of Concerns
- Keep business logic separate from UI components
- Use services for API communication
- Separate state management from component logic

### 2. Scalability
- Design for feature growth
- Use consistent patterns across features
- Plan for micro-frontend expansion

### 3. Maintainability
- Follow consistent naming conventions
- Document architectural decisions
- Use TypeScript for better type safety (future consideration)

### 4. Performance
- Implement lazy loading for routes and components
- Optimize bundle sizes
- Use memoization appropriately

## Architecture Review Checklist

Before implementing new features:
- [ ] Feature follows established folder structure
- [ ] Services are properly abstracted
- [ ] State management follows Redux Toolkit patterns
- [ ] Routes are properly configured
- [ ] Error handling is implemented
- [ ] Performance considerations are addressed
- [ ] Module Federation compatibility is maintained
