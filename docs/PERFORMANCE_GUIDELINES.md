# Performance Guidelines - NGA UI

## Table of Contents
- [React Performance](#react-performance)
- [Bundle Optimization](#bundle-optimization)
- [Network Performance](#network-performance)
- [Memory Management](#memory-management)
- [Rendering Optimization](#rendering-optimization)
- [Asset Optimization](#asset-optimization)
- [Monitoring & Metrics](#monitoring--metrics)

## React Performance

### 1. Component Optimization
```javascript
// ✅ Good - Use React.memo for expensive components
const ExpensiveUserCard = React.memo(({ user, onEdit }) => {
  const processedData = useMemo(() => {
    return expensiveDataProcessing(user);
  }, [user]);

  return (
    <div className="user-card">
      <h3>{user.name}</h3>
      <p>{processedData.summary}</p>
      <button onClick={() => onEdit(user.id)}>Edit</button>
    </div>
  );
});

// Custom comparison function for complex props
const UserList = React.memo(({ users, filters }) => {
  // Component logic
}, (prevProps, nextProps) => {
  return (
    prevProps.users.length === nextProps.users.length &&
    JSON.stringify(prevProps.filters) === JSON.stringify(nextProps.filters)
  );
});
```

### 2. Hook Optimization
```javascript
// ✅ Good - Optimize useCallback and useMemo
const UserManagement = ({ users, onUserUpdate }) => {
  // Memoize expensive calculations
  const filteredUsers = useMemo(() => {
    return users.filter(user => user.isActive);
  }, [users]);

  // Memoize event handlers
  const handleUserEdit = useCallback((userId, userData) => {
    onUserUpdate(userId, userData);
  }, [onUserUpdate]);

  // Memoize complex objects
  const tableConfig = useMemo(() => ({
    sortable: true,
    filterable: true,
    pageSize: 20,
  }), []);

  return (
    <UserTable 
      users={filteredUsers}
      onEdit={handleUserEdit}
      config={tableConfig}
    />
  );
};

// ❌ Avoid - Creating objects in render
const BadComponent = ({ users }) => {
  return (
    <UserTable 
      users={users}
      config={{ sortable: true }} // New object every render
      onEdit={(id, data) => updateUser(id, data)} // New function every render
    />
  );
};
```

### 3. State Management Optimization
```javascript
// ✅ Good - Normalize state structure
const initialState = {
  users: {
    byId: {},
    allIds: [],
  },
  ui: {
    loading: false,
    selectedUserId: null,
  },
};

// Efficient selectors
const selectUserById = createSelector(
  [(state) => state.users.byId, (state, userId) => userId],
  (usersById, userId) => usersById[userId]
);

const selectActiveUsers = createSelector(
  [(state) => state.users.byId, (state) => state.users.allIds],
  (usersById, allIds) => 
    allIds.filter(id => usersById[id]?.isActive).map(id => usersById[id])
);

// ❌ Avoid - Denormalized state
const badState = {
  users: [
    { id: 1, name: 'John', posts: [...] }, // Nested data
    { id: 2, name: 'Jane', posts: [...] },
  ],
};
```

## Bundle Optimization

### 1. Code Splitting
```javascript
// Route-based code splitting
import { lazy, Suspense } from 'react';

const UserManagement = lazy(() => import('pages/UserManagement'));
const BudgetDashboard = lazy(() => import('pages/BudgetDashboard'));
const Reports = lazy(() => import('pages/Reports'));

const App = () => (
  <Router>
    <Suspense fallback={<div>Loading...</div>}>
      <Routes>
        <Route path="/users" element={<UserManagement />} />
        <Route path="/budgets" element={<BudgetDashboard />} />
        <Route path="/reports" element={<Reports />} />
      </Routes>
    </Suspense>
  </Router>
);

// Component-based code splitting
const HeavyChart = lazy(() => import('shared/components/HeavyChart'));

const Dashboard = () => {
  const [showChart, setShowChart] = useState(false);

  return (
    <div>
      <h1>Dashboard</h1>
      {showChart && (
        <Suspense fallback={<div>Loading chart...</div>}>
          <HeavyChart />
        </Suspense>
      )}
      <button onClick={() => setShowChart(true)}>Show Chart</button>
    </div>
  );
};
```

### 2. Dynamic Imports
```javascript
// Dynamic import for heavy libraries
const loadChartLibrary = async () => {
  const { Chart } = await import('chart.js');
  return Chart;
};

// Conditional loading
const AdminPanel = () => {
  const [adminTools, setAdminTools] = useState(null);

  const loadAdminTools = async () => {
    if (user.role === 'admin') {
      const tools = await import('features/AdminTools');
      setAdminTools(tools.default);
    }
  };

  useEffect(() => {
    loadAdminTools();
  }, [user.role]);

  return adminTools ? <adminTools.Component /> : null;
};
```

### 3. Tree Shaking Optimization
```javascript
// ✅ Good - Import only what you need
import { debounce } from 'lodash/debounce';
import { format } from 'date-fns/format';

// ✅ Good - Use ES modules
import { Button, TextField } from '@mui/material';

// ❌ Avoid - Importing entire libraries
import _ from 'lodash'; // Imports entire library
import * as dateFns from 'date-fns'; // Imports everything
```

## Network Performance

### 1. API Optimization
```javascript
// Request deduplication
const requestCache = new Map();

const deduplicatedRequest = async (url, options = {}) => {
  const key = `${url}-${JSON.stringify(options)}`;
  
  if (requestCache.has(key)) {
    return requestCache.get(key);
  }
  
  const promise = fetch(url, options);
  requestCache.set(key, promise);
  
  // Clear cache after request completes
  promise.finally(() => {
    setTimeout(() => requestCache.delete(key), 1000);
  });
  
  return promise;
};

// Request batching
class RequestBatcher {
  constructor(batchSize = 10, delay = 100) {
    this.batchSize = batchSize;
    this.delay = delay;
    this.queue = [];
    this.timeoutId = null;
  }

  add(request) {
    return new Promise((resolve, reject) => {
      this.queue.push({ request, resolve, reject });
      
      if (this.queue.length >= this.batchSize) {
        this.flush();
      } else if (!this.timeoutId) {
        this.timeoutId = setTimeout(() => this.flush(), this.delay);
      }
    });
  }

  async flush() {
    if (this.queue.length === 0) return;
    
    const batch = this.queue.splice(0, this.batchSize);
    clearTimeout(this.timeoutId);
    this.timeoutId = null;

    try {
      const requests = batch.map(({ request }) => request);
      const responses = await Promise.all(requests);
      
      batch.forEach(({ resolve }, index) => {
        resolve(responses[index]);
      });
    } catch (error) {
      batch.forEach(({ reject }) => reject(error));
    }
  }
}
```

### 2. Caching Strategies
```javascript
// Service Worker caching
const CACHE_NAME = 'nga-ui-v1';
const urlsToCache = [
  '/',
  '/static/js/bundle.js',
  '/static/css/main.css',
];

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => cache.addAll(urlsToCache))
  );
});

// Memory caching with TTL
class MemoryCache {
  constructor(ttl = 5 * 60 * 1000) { // 5 minutes default
    this.cache = new Map();
    this.ttl = ttl;
  }

  set(key, value) {
    const expiry = Date.now() + this.ttl;
    this.cache.set(key, { value, expiry });
  }

  get(key) {
    const item = this.cache.get(key);
    if (!item) return null;
    
    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      return null;
    }
    
    return item.value;
  }
}

const apiCache = new MemoryCache();
```

### 3. Prefetching and Preloading
```javascript
// Link prefetching
const PrefetchLink = ({ to, children, ...props }) => {
  const handleMouseEnter = () => {
    // Prefetch route component
    import(`pages/${to}`);
  };

  return (
    <Link to={to} onMouseEnter={handleMouseEnter} {...props}>
      {children}
    </Link>
  );
};

// Data prefetching
const useDataPrefetch = (url, condition = true) => {
  useEffect(() => {
    if (condition) {
      // Prefetch data when component mounts
      fetch(url).then(response => {
        if (response.ok) {
          // Cache the response
          apiCache.set(url, response);
        }
      });
    }
  }, [url, condition]);
};
```

## Memory Management

### 1. Cleanup and Subscriptions
```javascript
// ✅ Good - Proper cleanup
const useWebSocket = (url) => {
  const [socket, setSocket] = useState(null);
  const [data, setData] = useState(null);

  useEffect(() => {
    const ws = new WebSocket(url);
    
    ws.onmessage = (event) => {
      setData(JSON.parse(event.data));
    };
    
    setSocket(ws);

    // Cleanup on unmount
    return () => {
      ws.close();
    };
  }, [url]);

  return { socket, data };
};

// Event listener cleanup
const useEventListener = (eventName, handler, element = window) => {
  useEffect(() => {
    element.addEventListener(eventName, handler);
    
    return () => {
      element.removeEventListener(eventName, handler);
    };
  }, [eventName, handler, element]);
};
```

### 2. Avoiding Memory Leaks
```javascript
// ✅ Good - Avoid closures over large objects
const processLargeData = (largeDataSet) => {
  const processedData = largeDataSet.map(item => ({
    id: item.id,
    name: item.name,
    // Only keep necessary fields
  }));
  
  return processedData;
};

// ✅ Good - Clear intervals and timeouts
const useInterval = (callback, delay) => {
  const intervalRef = useRef();

  useEffect(() => {
    if (delay !== null) {
      intervalRef.current = setInterval(callback, delay);
      
      return () => {
        clearInterval(intervalRef.current);
      };
    }
  }, [callback, delay]);
};
```

## Rendering Optimization

### 1. Virtual Scrolling
```javascript
// Virtual scrolling for large lists
import { FixedSizeList as List } from 'react-window';

const VirtualizedUserList = ({ users }) => {
  const Row = ({ index, style }) => (
    <div style={style}>
      <UserCard user={users[index]} />
    </div>
  );

  return (
    <List
      height={600}
      itemCount={users.length}
      itemSize={100}
      width="100%"
    >
      {Row}
    </List>
  );
};
```

### 2. Debouncing and Throttling
```javascript
// Debounced search
const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

const SearchInput = ({ onSearch }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  useEffect(() => {
    if (debouncedSearchTerm) {
      onSearch(debouncedSearchTerm);
    }
  }, [debouncedSearchTerm, onSearch]);

  return (
    <input
      type="text"
      value={searchTerm}
      onChange={(e) => setSearchTerm(e.target.value)}
      placeholder="Search..."
    />
  );
};
```

## Asset Optimization

### 1. Image Optimization
```javascript
// Lazy loading images
const LazyImage = ({ src, alt, ...props }) => {
  const [imageSrc, setImageSrc] = useState(null);
  const [imageRef, setImageRef] = useState();

  useEffect(() => {
    let observer;
    
    if (imageRef && imageSrc !== src) {
      if (IntersectionObserver) {
        observer = new IntersectionObserver(
          entries => {
            entries.forEach(entry => {
              if (entry.isIntersecting) {
                setImageSrc(src);
                observer.unobserve(imageRef);
              }
            });
          },
          { threshold: 0.1 }
        );
        observer.observe(imageRef);
      } else {
        // Fallback for browsers without IntersectionObserver
        setImageSrc(src);
      }
    }
    
    return () => {
      if (observer && observer.unobserve) {
        observer.unobserve(imageRef);
      }
    };
  }, [src, imageSrc, imageRef]);

  return (
    <div ref={setImageRef}>
      {imageSrc ? (
        <img src={imageSrc} alt={alt} {...props} />
      ) : (
        <div className="image-placeholder">Loading...</div>
      )}
    </div>
  );
};
```

### 2. Font Optimization
```css
/* Preload critical fonts */
@font-face {
  font-family: 'Roboto';
  src: url('/fonts/roboto-regular.woff2') format('woff2');
  font-display: swap; /* Improve loading performance */
  font-weight: 400;
  font-style: normal;
}

/* Use system fonts as fallback */
body {
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}
```

## Monitoring & Metrics

### 1. Performance Monitoring
```javascript
// Web Vitals monitoring
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

const sendToAnalytics = (metric) => {
  // Send to your analytics service
  console.log(metric);
};

// Monitor Core Web Vitals
getCLS(sendToAnalytics);
getFID(sendToAnalytics);
getFCP(sendToAnalytics);
getLCP(sendToAnalytics);
getTTFB(sendToAnalytics);

// Custom performance marks
const measureComponentRender = (componentName) => {
  performance.mark(`${componentName}-start`);
  
  return () => {
    performance.mark(`${componentName}-end`);
    performance.measure(
      `${componentName}-render`,
      `${componentName}-start`,
      `${componentName}-end`
    );
  };
};

// Usage in component
const ExpensiveComponent = () => {
  useEffect(() => {
    const endMeasure = measureComponentRender('ExpensiveComponent');
    return endMeasure;
  }, []);

  // Component logic
};
```

### 2. Bundle Analysis
```bash
# Analyze bundle size
npm run build
npx webpack-bundle-analyzer build/static/js/*.js

# Monitor bundle size over time
npm install --save-dev bundlesize
```

```json
// package.json
{
  "bundlesize": [
    {
      "path": "./build/static/js/*.js",
      "maxSize": "500kb"
    },
    {
      "path": "./build/static/css/*.css",
      "maxSize": "50kb"
    }
  ]
}
```

## Performance Checklist

Before deploying:
- [ ] Components are properly memoized
- [ ] Large lists use virtualization
- [ ] Images are optimized and lazy loaded
- [ ] Code splitting is implemented
- [ ] Bundle size is within limits
- [ ] API requests are optimized
- [ ] Memory leaks are prevented
- [ ] Performance metrics are monitored
- [ ] Critical resources are preloaded
- [ ] Unused code is eliminated
