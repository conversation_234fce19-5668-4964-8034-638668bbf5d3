# NGA UI - Project Rules & Guidelines

## Overview
This document provides a comprehensive overview of all rules and guidelines for the NGA UI project. It serves as a quick reference for developers and links to detailed documentation.

## 📋 Quick Reference

### Essential Commands
```bash
# Development
npm start                    # Start development server
npm run lint                 # Run ESLint
npm run stylelint           # Run Stylelint
npm run test:local          # Run tests locally
npm run test                # Run tests with coverage

# Build & Deploy
npm run build               # Build for production
npm run audit               # Security audit
```

### Project Structure
```
src/
├── core/           # Core utilities, services, state management
├── features/       # Feature-specific modules (self-contained)
├── pages/          # Page-level components
├── shared/         # Reusable components across features
├── layouts/        # Layout components
└── assets/         # Static assets (images, styles)
```

## 📚 Detailed Guidelines

### 1. [Coding Guidelines](docs/CODING_GUIDELINES.md)
**Essential Rules:**
- Use arrow function components with hooks
- Follow Airbnb ESLint configuration
- Define PropTypes for all components
- Use async/await for asynchronous operations
- Keep functions small and focused (single responsibility)
- Use meaningful variable and function names

**Import Order:**
1. React and external libraries
2. Internal modules (core, features, etc.)
3. Relative imports and styles

### 2. [Component Guidelines](docs/COMPONENT_GUIDELINES.md)
**Component Structure:**
```javascript
const ComponentName = ({ prop1, prop2 = 'default', onAction }) => {
  // 1. Hooks (useState, useEffect, custom hooks)
  // 2. Event handlers
  // 3. Effects
  // 4. Render helpers
  // 5. Main render
};

ComponentName.propTypes = {
  prop1: PropTypes.string.isRequired,
  prop2: PropTypes.string,
  onAction: PropTypes.func,
};
```

**Key Principles:**
- Separate presentational and container components
- Use React.memo for performance optimization
- Implement proper error boundaries
- Follow consistent file organization

### 3. [Architecture Guidelines](docs/ARCHITECTURE_GUIDELINES.md)
**Architecture Patterns:**
- Feature-based folder structure
- Redux Toolkit for state management
- Service layer for API communication
- Module Federation for micro-frontend architecture

**State Management:**
- Use Redux Toolkit slices
- Create reusable selectors
- Implement proper async thunk patterns
- Normalize state structure when needed

### 4. [Testing Guidelines](docs/TESTING_GUIDELINES.md)
**Testing Strategy:**
- Unit Tests (70%): Individual functions and components
- Integration Tests (20%): Component interactions
- E2E Tests (10%): Complete user workflows

**Testing Rules:**
- Test behavior, not implementation
- Use descriptive test names
- Mock external dependencies
- Aim for 80%+ code coverage on critical paths

### 5. [Git Workflow](docs/GIT_WORKFLOW.md)
**Branch Naming:**
- `feature/TICKET-ID-short-description`
- `bugfix/TICKET-ID-short-description`
- `hotfix/TICKET-ID-short-description`

**Commit Messages:**
```
<type>(<scope>): <description>

feat(user): add user profile editing functionality
fix(auth): resolve token refresh issue
docs(readme): update installation instructions
```

**PR Requirements:**
- Descriptive title and description
- All tests pass
- Code review approval
- No merge conflicts

### 6. [Style Guidelines](docs/STYLE_GUIDELINES.md)
**SCSS Rules:**
- Use BEM naming convention
- Leverage CSS custom properties for theming
- Follow mobile-first responsive design
- Use mixins for reusable patterns

**Example:**
```scss
.user-card {
  padding: $spacing-md;
  
  &__header {
    display: flex;
    justify-content: space-between;
  }
  
  &--highlighted {
    border: 2px solid var(--primary-500);
  }
}
```

### 7. [Security Guidelines](docs/SECURITY_GUIDELINES.md)
**Security Checklist:**
- Validate and sanitize all user inputs
- Use HTTPS in production
- Implement proper authentication/authorization
- Never expose sensitive data in client-side code
- Use Content Security Policy headers
- Regular dependency security audits

### 8. [Performance Guidelines](docs/PERFORMANCE_GUIDELINES.md)
**Performance Rules:**
- Use React.memo, useMemo, and useCallback appropriately
- Implement code splitting for routes and heavy components
- Optimize bundle size and eliminate unused code
- Use virtual scrolling for large lists
- Implement proper caching strategies

## 🔧 Configuration Files

### ESLint (.eslintrc.json)
- Extends Airbnb configuration
- Custom rules for React and project-specific needs
- Configured for JSX and modern JavaScript

### Stylelint (.stylelintrc.json)
- SCSS support with standard configuration
- BEM naming pattern enforcement
- Custom rules for project consistency

### Jest (jest.config.js)
- Custom test environment setup
- Module path mapping
- Coverage reporting configuration

### Husky Git Hooks
- **Pre-commit**: Runs linting (ESLint + Stylelint)
- **Pre-push**: Runs tests
- Ensures code quality before commits

## 🚀 Development Workflow

### Starting New Work
1. Pull latest changes from `develop`
2. Create feature branch: `git checkout -b feature/NGA-123-description`
3. Make changes following guidelines
4. Run tests and linting locally
5. Commit with conventional commit messages
6. Push and create Pull Request

### Code Review Process
1. Create PR with detailed description
2. Assign appropriate reviewers
3. Address feedback and update PR
4. Merge after approval (squash merge for small features)
5. Delete feature branch after merge

### Definition of Done
- [ ] Feature works as specified
- [ ] Code follows all guidelines
- [ ] Tests written and passing
- [ ] Code reviewed and approved
- [ ] Documentation updated if needed
- [ ] No console errors or warnings
- [ ] Performance impact considered
- [ ] Security implications reviewed

## 📊 Quality Gates

### Pre-commit Checks
- ESLint passes (no errors)
- Stylelint passes (no errors)
- Code formatting is consistent

### Pre-push Checks
- All tests pass
- No TypeScript errors (if applicable)
- Build succeeds

### PR Merge Requirements
- At least one approval from code owner
- All CI checks pass
- No merge conflicts
- Branch is up to date with target

## 🛠 Tools & Dependencies

### Core Technologies
- **React 18.2.0**: UI library
- **Redux Toolkit**: State management
- **Material-UI**: Component library
- **Ant Design**: Additional UI components
- **SCSS**: Styling with CSS modules

### Development Tools
- **ESLint**: Code linting (Airbnb config)
- **Stylelint**: CSS/SCSS linting
- **Jest**: Testing framework
- **Husky**: Git hooks
- **CRACO**: Create React App configuration

### Build & Deploy
- **Webpack Module Federation**: Micro-frontend architecture
- **CRACO**: Build configuration
- **Docker**: Containerization

## 📖 Additional Resources

### Documentation Links
- [React Documentation](https://reactjs.org/docs)
- [Redux Toolkit Documentation](https://redux-toolkit.js.org/)
- [Material-UI Documentation](https://mui.com/)
- [Jest Testing Documentation](https://jestjs.io/docs)

### Internal Resources
- [API Documentation](docs/API_DOCUMENTATION.md) *(to be created)*
- [Deployment Guide](docs/DEPLOYMENT_GUIDE.md) *(to be created)*
- [Troubleshooting Guide](docs/TROUBLESHOOTING.md) *(to be created)*

## 🔄 Continuous Improvement

### Regular Reviews
- Monthly architecture review meetings
- Quarterly dependency updates
- Annual security audit
- Performance monitoring and optimization

### Feedback Process
- Team retrospectives after major releases
- Developer experience surveys
- Code quality metrics tracking
- Performance benchmarking

## 📞 Support & Questions

### Getting Help
1. Check existing documentation
2. Search previous issues/discussions
3. Ask in team chat
4. Create issue for bugs or feature requests

### Contributing to Guidelines
1. Propose changes via PR to documentation
2. Discuss in team meetings
3. Update guidelines based on team consensus
4. Communicate changes to all team members

---

**Remember**: These guidelines are living documents. They should evolve with the project and team needs. Always prioritize code quality, security, and maintainability.
