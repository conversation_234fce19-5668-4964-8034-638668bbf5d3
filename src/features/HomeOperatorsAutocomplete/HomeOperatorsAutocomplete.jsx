import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { HTTPService } from 'core/services';
import Autocomplete from 'shared/Autocomplete';
import { useDispatch, useSelector } from 'react-redux';
import { TextField } from '@mui/material';
import getHomeOperatorsAction from 'features/HomeOperatorsAutocomplete/GetHomeOperators/actions';
import OperatorsLabel from 'shared/OperatorsLabel';
import getOperatorsFilterOptions from 'core/utilities/getOperatorsFilterOptions';

let listHomeOperatorsController = HTTPService.getController();

const HomeOperatorsAutocomplete = ({
  homeOperators, onChangeHomeOperators, className, label, limitTags,
}) => {
  const dispatch = useDispatch();
  const listOfHomeOperators = useSelector((state) => state.homeOperators.data);

  const optionKey = 'pmn_code';

  const getListOfHomeOperators = () => {
    HTTPService.cancelRequest(listHomeOperatorsController);

    listHomeOperatorsController = HTTPService.getController();
    dispatch(getHomeOperatorsAction(listHomeOperatorsController));
  };

  useEffect(() => {
    getListOfHomeOperators();
  }, []);

  const operatorsFilterOptions = getOperatorsFilterOptions();

  return (
    <Autocomplete
      className={`home-operators-autocomplete ${className}`}
      data-testid="home-operators-autocomplete"
      multiple
      limitTags={limitTags}
      autoComplete={false}
      selectOnFocus={false}
      options={listOfHomeOperators}
      getOptionLabel={(option) => (option[optionKey] ? option[optionKey] : '')}
      isOptionEqualToValue={(option, value) => option[optionKey] === value[optionKey]}
      value={homeOperators}
      onChange={onChangeHomeOperators}
      renderInput={(params) => <TextField {...params} placeholder="Search" label={label} />}
      optionKey={optionKey}
      filterOptions={operatorsFilterOptions}
      getLabel={(option) => <OperatorsLabel operator={option} />}
      enableInverseSelection
    />
  );
};

HomeOperatorsAutocomplete.propTypes = {
  className: PropTypes.string,
  homeOperators: PropTypes.instanceOf(Object),
  onChangeHomeOperators: PropTypes.func.isRequired,
  label: PropTypes.string,
  limitTags: PropTypes.number,
};

HomeOperatorsAutocomplete.defaultProps = {
  className: '',
  homeOperators: null,
  label: null,
  limitTags: 1,
};
export default HomeOperatorsAutocomplete;
