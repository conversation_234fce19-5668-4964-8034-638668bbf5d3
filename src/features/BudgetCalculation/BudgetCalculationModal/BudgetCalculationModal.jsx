import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';

import CustomModal from '@nv2/nv2-pkg-js-shared-components/lib/CustomModal';

import {
  useBudgetCalculationContext,
} from 'features/BudgetCalculation/BudgetCalculationContextProvider';

import PerfectScrollbar from 'react-perfect-scrollbar';
import {
  budgetCalculationModalModifier,
  initialTriggerConfirmButton,
} from './constants';
import './BudgetCalculationModal.scss';
import BudgetCalculationDetails from './BudgetCalculationDetails';
import BudgetCalculationRun from './BudgetCalculationRun';
import BudgetComponentsState from './BudgetComponentsState';
import BudgetCalculationSteps from './BudgetCalculationSteps';

const BudgetCalculationModal = ({ isCalculationRunning }) => {
  const [isOpenBudgetCalculationModal, setIsOpenBudgetCalculationModal] = useState(false);
  const [triggerConfirmButton, setTriggerConfirmButton] = useState(initialTriggerConfirmButton);
  const isDisabledBudgetCalculationConfirmButton = false;
  const {
    triggerBudgetCalculationModal, setTriggerBudgetCalculationModal,
  } = useBudgetCalculationContext();

  const isBudgetCalculationRunLoading = useSelector(
    (state) => state.budgetCalculationRun.isLoading,
  );

  const budgetCalculationModalTitle = isCalculationRunning
    ? 'Calculation is running...'
    : 'Calculate budget';

  const closeModal = () => {
    setTriggerBudgetCalculationModal(0);
    setIsOpenBudgetCalculationModal(false);
  };

  const openBudgetCalculationModal = () => {
    setIsOpenBudgetCalculationModal(true);
  };

  useEffect(() => {
    if (triggerBudgetCalculationModal) {
      openBudgetCalculationModal();
    } else {
      closeModal();
    }
  }, [triggerBudgetCalculationModal]);

  return (
    <CustomModal
      isOpen={isOpenBudgetCalculationModal}
      buttonDisable={isDisabledBudgetCalculationConfirmButton}
      onClickConfirm={() => setTriggerConfirmButton(triggerConfirmButton + 1)}
      onClickCancel={closeModal}
      title={budgetCalculationModalTitle}
      handleOpen={closeModal}
      modalClass={budgetCalculationModalModifier}
      dataTestid={budgetCalculationModalModifier}
      showButtons={!isCalculationRunning}
      isLoading={isBudgetCalculationRunLoading}
      disableBackdropClick={isBudgetCalculationRunLoading}
      showCircularProgressInPreloader={false}
      showConfirmButtonPreloader={false}
    >
      <PerfectScrollbar options={{ suppressScrollX: false }}>
        <BudgetCalculationDetails
          isCalculationRunning={isCalculationRunning}
        />
        <BudgetCalculationRun
          isCalculationRunning={isCalculationRunning}
          triggerConfirmButton={triggerConfirmButton}
          resetTriggerConfirmButton={() => setTriggerConfirmButton(initialTriggerConfirmButton)}
        />
        <BudgetComponentsState
          isCalculationRunning={isCalculationRunning}
        />
        <BudgetCalculationSteps
          isCalculationRunning={isCalculationRunning}
        />
      </PerfectScrollbar>
    </CustomModal>
  );
};

BudgetCalculationModal.propTypes = {
  isCalculationRunning: PropTypes.bool,
};

BudgetCalculationModal.defaultProps = {
  isCalculationRunning: false,
};

export default BudgetCalculationModal;
