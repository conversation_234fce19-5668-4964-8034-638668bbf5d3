import React, { useEffect } from 'react';
import PropTypes from 'prop-types';

import { intersectionBy } from 'lodash';
import { TableCell, TableRow, TextField } from '@mui/material';

import Autocomplete from 'shared/Autocomplete';
import OperatorsLabel from 'shared/OperatorsLabel';
import FormattedNumber from 'shared/FormattedNumber';

import {
  commitmentDistributionParameters,
  commitmentDistributionParametersFields,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountParameters/CommitmentDistribution/CommitmentDistributionParameters/constants';
import {
  errorModifier,
  optionKey,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/constants';
import {
  CommitmentDistributionParametersControlsDelete,
} from 'pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountParameters/CommitmentDistribution/CommitmentDistributionParameters/CommitmentDistributionParametersControls';

import {
  commitmentDistributionAutocompleteModifier,
  commitmentDistributionInputModifier,
  commitmentDistributionSplitInputModifier,
} from './constants';

const CommitmentDistributionParameter = ({
  homeOperatorsOptions,
  partnerOperatorsOptions,
  homeOperators,
  partnerOperators,
  charge,
  index,
  setFormikValues,
  setFormikErrors,
  formikErrors,
  setTriggerDeleteCommitmentDistributionParameterBtn,
  isBtnDeleteExist,
}) => {
  const getErrorModifier = (field) => (formikErrors?.[commitmentDistributionParameters]?.[index]?.[field] ? errorModifier : '');

  const resetCommitmentDistributionParameterError = (fieldName) => {
    if (formikErrors?.[commitmentDistributionParameters]?.[index]?.[fieldName]) {
      const newErrors = () => {
        const newCommitmentDistributionParametersErrors = [
          ...formikErrors[commitmentDistributionParameters],
        ];

        delete newCommitmentDistributionParametersErrors[index][fieldName];

        return {
          ...formikErrors,
          [commitmentDistributionParameters]: newCommitmentDistributionParametersErrors,
        };
      };

      setFormikErrors(newErrors());
    }
  };

  const changeCommitmentDistributionParameter = ({ fieldName, value }) => {
    resetCommitmentDistributionParameterError(fieldName);

    setFormikValues((values) => {
      const newCommitmentDistributionParameters = [...values[commitmentDistributionParameters]];

      newCommitmentDistributionParameters[index][fieldName] = value;

      return {
        ...values,
        [commitmentDistributionParameters]: newCommitmentDistributionParameters,
      };
    });
  };

  const checkHomeOperatorsIntersection = () => {
    const newValues = intersectionBy(
      homeOperators, homeOperatorsOptions, ({ id }) => id);

    changeCommitmentDistributionParameter({
      fieldName: commitmentDistributionParametersFields.homeOperators,
      value: newValues,
    });
  };

  const checkPartnerOperatorsIntersection = () => {
    const newValues = intersectionBy(
      partnerOperators, partnerOperatorsOptions, ({ id }) => id);

    changeCommitmentDistributionParameter({
      fieldName: commitmentDistributionParametersFields.partnerOperators,
      value: newValues,
    });
  };

  useEffect(() => {
    checkHomeOperatorsIntersection();
  }, [homeOperatorsOptions]);

  useEffect(() => {
    checkPartnerOperatorsIntersection();
  }, [partnerOperatorsOptions]);

  return (
    <TableRow>
      <TableCell>
        <Autocomplete
          name={commitmentDistributionParametersFields.homeOperators}
          data-testid={commitmentDistributionParametersFields.homeOperators}
          className={`${commitmentDistributionAutocompleteModifier} ${getErrorModifier(commitmentDistributionParametersFields.homeOperators)}`}
          autoComplete={false}
          selectOnFocus={false}
          options={homeOperatorsOptions}
          getOptionLabel={(option) => (option[optionKey.pmnCode] ? option[optionKey.pmnCode] : '')}
          /* eslint-disable-next-line max-len */
          isOptionEqualToValue={(option, value) => option[optionKey.pmnCode] === value[optionKey.pmnCode]}
          renderInput={(params) => <TextField {...params} variant="outlined" placeholder="Search" />}
          value={homeOperators}
          onChange={(e, value) => changeCommitmentDistributionParameter({
            fieldName: commitmentDistributionParametersFields.homeOperators,
            value,
          })}
          multiple
          optionKey={optionKey.pmnCode}
          getLabel={(option) => <OperatorsLabel operator={option} />}
          enableInverseSelection
        />
      </TableCell>
      <TableCell>
        <Autocomplete
          name={commitmentDistributionParametersFields.partnerOperators}
          data-testid={commitmentDistributionParametersFields.partnerOperators}
          className={`${commitmentDistributionAutocompleteModifier} ${getErrorModifier(commitmentDistributionParametersFields.partnerOperators)}`}
          autoComplete={false}
          selectOnFocus={false}
          options={partnerOperatorsOptions}
          getOptionLabel={(option) => (option[optionKey.pmnCode] ? option[optionKey.pmnCode] : '')}
          /* eslint-disable-next-line max-len */
          isOptionEqualToValue={(option, value) => option[optionKey.pmnCode] === value[optionKey.pmnCode]}
          renderInput={(params) => <TextField {...params} variant="outlined" placeholder="Search" />}
          value={partnerOperators}
          onChange={(e, value) => changeCommitmentDistributionParameter({
            fieldName: commitmentDistributionParametersFields.partnerOperators,
            value,
          })}
          multiple
          optionKey={optionKey.pmnCode}
          getLabel={(option) => <OperatorsLabel operator={option} />}
          enableInverseSelection
        />
      </TableCell>
      <TableCell>
        <FormattedNumber
          isInput
          valueIsNumericString
          value={charge || ''}
          onValueChange={({ value }) => changeCommitmentDistributionParameter({
            fieldName: commitmentDistributionParametersFields.charge,
            value: value === '' ? null : value,
          })}
          className={`${commitmentDistributionInputModifier} ${getErrorModifier(commitmentDistributionParametersFields.charge)} ${commitmentDistributionSplitInputModifier}`}
          data-testid={commitmentDistributionParametersFields.charge}
        />
      </TableCell>
      <TableCell>
        {isBtnDeleteExist
            && (
            <CommitmentDistributionParametersControlsDelete
              setTriggerDeleteCommitmentDistributionParameterBtn={
            setTriggerDeleteCommitmentDistributionParameterBtn
          }
              index={index}
            />
            )}
      </TableCell>
    </TableRow>
  );
};

CommitmentDistributionParameter.propTypes = {
  homeOperatorsOptions: PropTypes.instanceOf(Array),
  partnerOperatorsOptions: PropTypes.instanceOf(Array),
  homeOperators: PropTypes.instanceOf(Array),
  partnerOperators: PropTypes.instanceOf(Array),
  charge: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  index: PropTypes.number.isRequired,
  setFormikValues: PropTypes.func,
  setFormikErrors: PropTypes.func,
  formikErrors: PropTypes.instanceOf(Object),
  setTriggerDeleteCommitmentDistributionParameterBtn: PropTypes.func.isRequired,
  isBtnDeleteExist: PropTypes.bool,
};

CommitmentDistributionParameter.defaultProps = {
  homeOperatorsOptions: [],
  partnerOperatorsOptions: [],
  homeOperators: null,
  partnerOperators: null,
  charge: null,
  setFormikValues: () => {},
  setFormikErrors: () => {},
  formikErrors: {},
  isBtnDeleteExist: false,
};

export default CommitmentDistributionParameter;
