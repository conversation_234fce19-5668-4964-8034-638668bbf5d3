import React from 'react';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';

import { TextField } from '@mui/material';

import Autocomplete from 'shared/Autocomplete';
import OperatorsLabel from 'shared/OperatorsLabel';
import getOperatorsFilterOptions from 'core/utilities/getOperatorsFilterOptions';

const HomeOperatorsAutocomplete = ({
  onHomeOperatorsChange, homeOperatorsValue, label, limitTags, ...props
}) => {
  const homeOperators = useSelector((state) => state.budgetParameters.data.home_operators);
  const optionKey = 'pmn_code';

  const operatorsFilterOptions = getOperatorsFilterOptions();

  return (
    <Autocomplete
      data-testid="home-operators-autocomplete"
      className="home-operators-autocomplete"
      limitTags={limitTags}
      autoComplete={false}
      selectOnFocus={false}
      options={homeOperators}
      getOptionLabel={(option) => (option[optionKey] ? option[optionKey] : '')}
      isOptionEqualToValue={(option, value) => option[optionKey] === value[optionKey]}
      renderInput={(params) => <TextField {...params} variant="outlined" placeholder="Search" label={label} />}
      value={homeOperatorsValue}
      onChange={(e, value) => onHomeOperatorsChange(value)}
      multiple
      optionKey={optionKey}
      filterOptions={operatorsFilterOptions}
      getLabel={(option) => <OperatorsLabel operator={option} />}
      enableInverseSelection
      {...props}
    />
  );
};

HomeOperatorsAutocomplete.propTypes = {
  onHomeOperatorsChange: PropTypes.func,
  homeOperatorsValue: PropTypes.instanceOf(Array),
  label: PropTypes.string,
  limitTags: PropTypes.number,
};

HomeOperatorsAutocomplete.defaultProps = {
  onHomeOperatorsChange: () => {},
  homeOperatorsValue: [],
  label: null,
  limitTags: 1,
};

export default HomeOperatorsAutocomplete;
