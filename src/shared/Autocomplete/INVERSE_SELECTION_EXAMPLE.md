# Inverse Selection Feature for Multi-Select Autocomplete

## Overview

The Autocomplete component now supports an "Inverse Selection" feature that allows users to easily select all unselected options with a single click. This is particularly useful when working with large lists where you want to select most items except for a few.

## Use Case

This feature is especially helpful in scenarios like discount parameter configuration where you need to:
1. Select 1 affiliate for the first discount parameter
2. Select 4 other affiliates for the second discount parameter  
3. Select the remaining 12 affiliates for the third discount parameter

Instead of manually clicking each of the 12 remaining affiliates, you can:
1. Select the 5 affiliates you DON'T want
2. Click "Inverse Selection" to automatically select the remaining 12

## Usage

### Basic Example

```jsx
import Autocomplete from 'shared/Autocomplete';

const AffiliateSelector = () => {
  const [selectedAffiliates, setSelectedAffiliates] = useState([]);
  
  const affiliateOptions = [
    { id: '1', name: 'Affiliate A' },
    { id: '2', name: 'Affiliate B' },
    { id: '3', name: 'Affiliate C' },
    // ... more affiliates
  ];

  return (
    <Autocomplete
      options={affiliateOptions}
      value={selectedAffiliates}
      onChange={(e, value) => setSelectedAffiliates(value)}
      renderInput={(params) => (
        <TextField {...params} label="Select Affiliates" />
      )}
      multiple
      optionKey="name"
      getLabel={(option) => option.name}
      enableSelectAll={true}
      enableInverseSelection={true} // Enable inverse selection
    />
  );
};
```

### Integration with Discount Parameters

```jsx
// In a discount parameter component
<Autocomplete
  name="affiliates"
  options={affiliateOptions}
  value={selectedAffiliates}
  onChange={(e, value) => handleAffiliateChange(value)}
  renderInput={(params) => (
    <TextField {...params} label="Group Affiliates" />
  )}
  multiple
  optionKey="id"
  getLabel={(option) => option.name}
  enableSelectAll={true}
  enableInverseSelection={true}
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `enableInverseSelection` | boolean | `false` | Enables the inverse selection option in the dropdown |
| `enableSelectAll` | boolean | `true` | Must be true for inverse selection to work |

## Behavior

- **Visibility**: The "Inverse Selection" option appears when:
  - `enableInverseSelection` is `true`
  - The dropdown is in multi-select mode
  - Always visible (like "Select All")

- **Functionality**: When clicked, "Inverse Selection":
  - Only works when at least one item is selected
  - Deselects all currently selected items
  - Selects all currently unselected items
  - Effectively "inverts" the current selection
  - Does nothing if no items are selected (appears disabled)

- **Visual Styling**: The inverse selection option is styled with:
  - Bold, italic text
  - Primary color highlighting when enabled
  - Grayed out appearance when disabled (no selections)
  - Distinct appearance from regular options

## Technical Implementation

The feature extends the existing `getEnableSelectAllConfig` utility to:
1. Add inverse selection option configuration
2. Handle inverse selection logic in the change handler
3. Provide appropriate visual styling and behavior

The implementation is backward compatible and doesn't affect existing functionality.
