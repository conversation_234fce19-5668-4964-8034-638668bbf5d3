import React, { useMemo } from 'react';
import PropTypes from 'prop-types';

import { GrFormDown } from 'react-icons/gr';
import { AiOutlineClose } from 'react-icons/ai';
import {
  Autocomplete as MuiAutocomplete,
  Checkbox,
  TextField,
} from '@mui/material';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';

import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import CheckBoxIcon from '@mui/icons-material/CheckBox';

import MultipleAutocompleteTags
  from 'shared/Autocomplete/MultipleAutocompleteTags';
import MultipleAutocompleteTagsWithLimitation
  from 'shared/Autocomplete/MultipleAutocompleteTagsWithLimitation';

import getEnableSelectAllConfig from './getEnableSelectAllConfig';

import { autocompleteModifier } from './constants';

const Autocomplete = ({
  className,
  options,
  value,
  onChange,
  renderInput,
  multiple,
  optionKey,
  getLabel,
  enableSelectAll,
  enableInverseSelection,
  ...props
}) => {
  const {
    isSelectAllOption,
    isInverseSelectionOption,
    isAllSelected,
    hasPartialSelection,
    optionsWhenEnableSelectAll,
    getNewValueWhenEnableSelectAll,
    getOptionClassName,
  } = getEnableSelectAllConfig({
    value,
    optionKey,
    options,
    enableInverseSelection,
  });

  const renderOption = (optionProps, option, { selected }) => {
    let isChecked = selected;
    let isDisabled = false;

    if (isSelectAllOption(option)) {
      isChecked = isAllSelected;
    } else if (isInverseSelectionOption(option)) {
      // Inverse selection is "checked" when there's a partial selection
      isChecked = hasPartialSelection;
      // Disable inverse selection when no items are selected
      isDisabled = !value || value.length === 0;
    }

    return (
      <li
        {...optionProps}
        className={`${optionProps.className} ${getOptionClassName(option)} ${isDisabled ? 'disabled' : ''}`}
        style={{
          ...optionProps.style,
          opacity: isDisabled ? 0.5 : 1,
          cursor: isDisabled ? 'not-allowed' : 'pointer',
        }}
      >
        <Checkbox
          icon={<CheckBoxOutlineBlankIcon fontSize="small" />}
          checkedIcon={<CheckBoxIcon fontSize="small" />}
          style={{ marginRight: 8 }}
          checked={isChecked}
          disabled={isDisabled}
        />
        {getLabel(option) || option[optionKey] || option}
      </li>
    );
  };

  const additionPropsForMultiple = ((multiple && optionKey) || (multiple && getLabel))
    ? { renderOption, multiple, disableCloseOnSelect: true }
    : {};

  const handleRenderTags = (tagValue, getTagProps) => {
    const isDefaultTagsView = tagValue.length <= props.limitTags;

    if (isDefaultTagsView) {
      return (
        <MultipleAutocompleteTags
          tagValue={tagValue}
          getTagProps={getTagProps}
          optionKey={optionKey}
          getOptionLabel={props.getOptionLabel}
        />
      );
    }

    return (
      <MultipleAutocompleteTagsWithLimitation
        tagValue={tagValue}
        getTagProps={getTagProps}
        optionKey={optionKey}
        limitTags={props.limitTags}
        getOptionLabel={props.getOptionLabel}
      />
    );
  };

  const autocompleteOptions = useMemo(() => (multiple && enableSelectAll && !!options.length
    ? optionsWhenEnableSelectAll
    : options), [options]);

  const onAutocompleteChange = (event, newValues, reason, details) => {
    const newAutocompleteValues = multiple && enableSelectAll
      ? getNewValueWhenEnableSelectAll(newValues)
      : newValues;

    onChange(event, newAutocompleteValues, reason, details);
  };

  return (
    <MuiAutocomplete
      data-testid={autocompleteModifier}
      className={`${autocompleteModifier} ${className}`}
      clearIcon={<AiOutlineClose color={styles.darkColor300} size={16} />}
      options={autocompleteOptions}
      popupIcon={<GrFormDown color={styles.darkColor300} size={16} />}
      value={value}
      onChange={onAutocompleteChange}
      renderInput={renderInput}
      renderTags={handleRenderTags}
      {...props}
      {...additionPropsForMultiple}
    />
  );
};

Autocomplete.propTypes = {
  className: PropTypes.string,
  options: PropTypes.instanceOf(Array),
  onChange: PropTypes.func,
  renderInput: PropTypes.func,
  value: PropTypes.oneOfType([
    PropTypes.instanceOf(Array),
    PropTypes.shape({
      code: PropTypes.string,
    }),
    PropTypes.string,
  ]),
  multiple: PropTypes.bool,
  optionKey: PropTypes.string,
  getLabel: PropTypes.func,
  limitTags: PropTypes.number,
  getOptionLabel: PropTypes.func,
  enableSelectAll: PropTypes.bool,
  enableInverseSelection: PropTypes.bool,
};

Autocomplete.defaultProps = {
  className: '',
  options: [],
  onChange: () => {},
  renderInput: (params) => <TextField {...params} />,
  value: null,
  multiple: false,
  optionKey: '',
  getLabel: () => '',
  limitTags: 2,
  getOptionLabel: (option) => option.label ?? option,
  enableSelectAll: true,
  enableInverseSelection: false,
};

export default Autocomplete;
