import React, { useState } from 'react';
import { TextField } from '@mui/material';
import Autocomplete from './Autocomplete';

// Test component to verify inverse selection functionality
const InverseSelectionTest = () => {
  const [selectedItems, setSelectedItems] = useState([]);

  const testOptions = [
    { id: '1', name: 'Option 1' },
    { id: '2', name: 'Option 2' },
    { id: '3', name: 'Option 3' },
    { id: '4', name: 'Option 4' },
    { id: '5', name: 'Option 5' },
    { id: '6', name: 'Option 6' },
    { id: '7', name: 'Option 7' },
    { id: '8', name: 'Option 8' },
  ];

  return (
    <div style={{ padding: '20px', maxWidth: '400px' }}>
      <h3>Inverse Selection Test</h3>
      <p>Instructions:</p>
      <ol>
        <li>
          Open the dropdown - you should see both &quot;Select All&quot; and &quot;Inverse Selection&quot;
        </li>
        <li>
          When no items are selected, &quot;Inverse Selection&quot; appears disabled (grayed out)
        </li>
        <li>Select 1-3 options from the dropdown</li>
        <li>
          Open the dropdown again - &quot;Inverse Selection&quot; should now be enabled
        </li>
        <li>
          Click &quot;Inverse Selection&quot; to invert your selection
        </li>
      </ol>

      <Autocomplete
        options={testOptions}
        value={selectedItems}
        onChange={(e, value) => setSelectedItems(value)}
        renderInput={(params) => (
          <TextField {...params} label="Test Multi-Select with Inverse Selection" />
        )}
        multiple
        optionKey="name"
        getLabel={(option) => option.name}
        enableSelectAll
        enableInverseSelection
      />

      <div style={{ marginTop: '20px' }}>
        <strong>Selected items:</strong>
        <ul>
          {selectedItems.map((item) => (
            <li key={item.id}>{item.name}</li>
          ))}
        </ul>
        <p>
          Total selected:
          {' '}
          {selectedItems.length}
          {' '}
          /
          {' '}
          {testOptions.length}
        </p>
      </div>
    </div>
  );
};

export default InverseSelectionTest;
