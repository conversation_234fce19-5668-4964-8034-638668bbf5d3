import React from 'react';
import PropTypes from 'prop-types';

import {
  TableBody,
  TableCell,
  TableRow,
} from '@mui/material';

import { useAppContext } from 'AppContextProvider';
import { modifiers } from 'shared/TrafficValues/TrafficWorldmapDashboard/TrafficCountryOperatorsValues/TrafficCountryOperatorsValuesTable/constants';
import FormattedNumber from 'shared/FormattedNumber';
import Marker from 'shared/Marker';

const TrafficCountryOperatorsValuesTableBody = ({
  data, canHavePreviousYear, operatorsList, isComparisonMode, trafficComparisonData, trafficIdKey,
}) => {
  const { primaryColor, getBrandColors } = useAppContext();

  const getOperatorRow = (record) => {
    const style = { ':hover': { backgroundColor: getBrandColors(primaryColor)[50] } };
    const pmnCode = operatorsList.find(({ id }) => id === record.operator_id)?.pmn_code;
    const getTableCellValues = (key) => (data.map(
      (traffic) => {
        const value = traffic.records.find((r) => r.operator_id === record.operator_id)?.[key];

        return (
          <div className={modifiers.tableCellItem} key={traffic[trafficIdKey]}>
            <FormattedNumber
              value={value}
              isNeedRound
            />
          </div>
        );
      },
    ));

    return (
      <TableRow className={modifiers.tableRow} style={style}>
        <TableCell className={modifiers.tableCell}>
          <div className={modifiers.tableCellItemOperatorWrap}>
            <div className={modifiers.tableCellItemOperator}>{pmnCode}</div>
            {isComparisonMode && (
            <div>
              {data.map(
                (traffic) => {
                  const trafficId = traffic[trafficIdKey];
                  const markerColor = trafficComparisonData.find(
                    (trafficComparison) => +trafficComparison.value.id === trafficId)?.markerColor;

                  return (
                    <div className={modifiers.tableCellItem} key={traffic[trafficIdKey]}>
                      <Marker backgroundColor={markerColor} />
                    </div>
                  );
                },
              )}
            </div>
            )}
          </div>
        </TableCell>
        <TableCell className={modifiers.tableCell}>
          {getTableCellValues('value')}
        </TableCell>
        {canHavePreviousYear && (
        <>
          <TableCell className={modifiers.tableCell}>
            {getTableCellValues('previous_year_value')}
          </TableCell>
          <TableCell className={modifiers.tableCell}>
            {getTableCellValues('dynamic')}
          </TableCell>
        </>
        )}
        <TableCell className={modifiers.tableCell}>
          {getTableCellValues('country_share')}
        </TableCell>
      </TableRow>
    );
  };

  return (
    <TableBody className={modifiers.tableBody}>
      {data[0]?.records.map(
        (record) => (
          <React.Fragment key={record.operator_id}>
            {getOperatorRow(record)}
          </React.Fragment>
        ))}
    </TableBody>
  );
};

TrafficCountryOperatorsValuesTableBody.propTypes = {
  data: PropTypes.arrayOf(PropTypes.shape({
    records: PropTypes.arrayOf(PropTypes.shape({
      country_share: PropTypes.string,
      dynamic: PropTypes.string,
      operator_id: PropTypes.number,
      previous_year_value: PropTypes.string,
      value: PropTypes.string,
    })),
  })),
  canHavePreviousYear: PropTypes.bool,
  operatorsList: PropTypes.instanceOf(Array),
  isComparisonMode: PropTypes.bool,
  trafficComparisonData: PropTypes.instanceOf(Array),
  trafficIdKey: PropTypes.string,
};

TrafficCountryOperatorsValuesTableBody.defaultProps = {
  canHavePreviousYear: false,
  data: [{
    country_share: 0,
    dynamic: 0,
    operator_id: 0,
    previous_year_value: 0,
    value: 0,
  }],
  operatorsList: [],
  isComparisonMode: false,
  trafficComparisonData: [],
  trafficIdKey: 'budget_id',
};

export default TrafficCountryOperatorsValuesTableBody;
