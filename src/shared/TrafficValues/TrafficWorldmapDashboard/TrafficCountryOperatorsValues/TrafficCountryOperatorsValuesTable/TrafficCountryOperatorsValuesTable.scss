.traffic-country-operators-values-table {
  &__wrap {
    position: relative;
    border-top: 1px solid $border-color;

    .scrollbar-container {
      max-height: 400px;
    }
  }

  &__cell {
    height: 40px;
    border-top: 1px solid $light-color-200;
    border-right: none;
    padding: 0 14px !important;
    text-align: right !important;
    font-size: 12px !important;
    white-space: nowrap;
    color: $dark-color-500 !important;

    &:first-child {
      text-align: left !important;
    }

    &-item {
      display: flex;
      height: 40px;
      align-items: center;
      justify-content: flex-end;

      &-operator {
        height: 100%;
        display: flex;
        align-items: center;

        &-wrap {
          display: flex;
          height: 100%;
          width: 70px;
          justify-content: space-between;
        }
      }
    }
  }

  &__thead {
    background-color: $light-color-100;

    .traffic-country-operators-values-table__cell {
      position: sticky;
      top: 0;
      z-index: 100;
      border-top: 0;
    }
  }

  &__tfooter {
    display: table-row-group !important;

    .traffic-country-operators-values-table__cell {
      font-weight: bold;
    }
  }

  &__failure {
    border: none !important;

    &:hover {
      background-color: transparent !important;
    }

    &-placeholder {
      align-items: center;
      justify-content: center;
      text-align: center;
    }
  }

  .no-data__wrap {
    height: 180px;
  }
}
